server:
  port: 8081
  servlet:
    context-path: /aiLive
    encoding:
      charset: UTF-8
      force: true
      enabled: true
redis:
  # 地址
  host: *************
  port: 30379
  database: 0
  # 密码
  password: password4redis

spring:
  application:
    name: ai-live-api
  profiles:
    # 切换配置，修改这里
    active: dev
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.unnet.api.service.impl.ApiLogServiceImpl: OFF
    root: INFO
    com.unnet.api: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Swagger配置
springdoc:
  api-docs:
    enabled: true  # 是否开启API文档
    path: /v3/api-docs  # API文档路径
  swagger-ui:
    enabled: true  # 是否开启Swagger UI界面
    path: /swagger-ui.html  # Swagger UI路径
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.unnet.api.controller  # 要扫描的包
  paths-to-match: /**, /api/**  # 要匹配的路径
  cache:
    disabled: true


# 缓存配置
spring.cache:
  type: caffeine
  caffeine:
    spec: maximumSize=100,expireAfterWrite=10m


#mapper文件存放地址 匹配mapper包下的 后缀为 Mapper 的xml文件
mybatis:
  mapper-locations: classpath:mapper/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: true
