package com.unnet.api.aspect;

import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class CustomExceptionAspect {
    @Pointcut("execution(* com.unnet.api.service.impl.ApiLogServiceImpl.saveApiLog(..))")
    public void apiLogSavePointcut() {}

    @AfterThrowing(pointcut = "apiLogSavePointcut()", throwing = "ex")
    public void handleDataAccessResourceFailureException(DataAccessResourceFailureException ex) {
    }
}
