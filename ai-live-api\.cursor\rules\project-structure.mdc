---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

## 代码组织

项目遵循标准的Spring Boot Maven项目结构：

```
src/main/java/com/unnet/api/
├── AiLiveApiApplication.java    # Spring Boot主应用类
├── aspect/                      # AOP切面
│   └── LoggingAspect.java      # 日志记录切面
├── config/                      # 配置类
│   ├── AppProperties.java      # 应用配置属性
│   └── CacheConfig.java        # 缓存配置
├── controller/                  # 控制器层
│   └── (各种Controller)
├── service/                     # 业务逻辑层
│   └── (各种Service)
├── dto/                         # 数据传输对象
│   ├── ApiResponse.java        # 通用API响应
│   └── (各种Request/Response)
├── exception/                   # 异常处理
│   ├── BusinessException.java  # 业务异常
│   ├── ErrorResponse.java      # 错误响应
│   └── GlobalExceptionHandler.java # 全局异常处理
├── filter/                      # 过滤器
│   ├── ApiKeyAuthFilter.java   # API Key认证
│   └── ReplayAttackFilter.java # 防重放攻击
├── aspect/                      # 切面
├── util/                        # 工具类
└── (其他包)
```

## 层次架构

1. **Controller层**: 处理HTTP请求，参数校验
2. **Service层**: 业务逻辑处理
3. **DTO层**: 数据传输对象，用于API请求/响应
4. **Filter层**: 请求过滤和预处理
5. **Exception层**: 异常处理和错误响应
6. **Config层**: 配置和Bean定义
7. **Aspect层**: 横切关注点(日志、监控等)
8. **Util层**: 工具方法

## 命名约定

- Controller: `*Controller.java`
- Service: `*Service.java` (接口) 和 `*ServiceImpl.java` (实现)
- DTO: `*Request.java`, `*Response.java`, `*DTO.java`
- Exception: `*Exception.java`
- Config: `*Config.java`
- Filter: `*Filter.java`
