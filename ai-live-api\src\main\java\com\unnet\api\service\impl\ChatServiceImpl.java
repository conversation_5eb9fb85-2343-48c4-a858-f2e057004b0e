package com.unnet.api.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.api.dto.*;
import com.unnet.api.dto.internal.ConverseInternalResponse;
import com.unnet.api.dto.internal.CreateSessionInternalResponse;
import com.unnet.api.dto.ragflow.*;
import com.unnet.api.entity.DialogEntity;
import com.unnet.api.entity.KnowledgebaseEntity;
import com.unnet.api.repository.DialogRepository;
import com.unnet.api.repository.DocumentRepository;
import com.unnet.api.repository.KnowledgebaseRepository;
import com.unnet.api.service.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 聊天服务类
 */
@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    private final DialogRepository dialogRepository;
    private final DocumentRepository documentRepository;
    private final KnowledgebaseRepository knowledgebaseRepository;
    @Value("${llm.base-url:http://172.20.20.16:9997/v1}")
    private String baseUrl;

    @Value("${ragflow.api.baseUrl}")
    private String ragflowBaseUrl;

    @Value("${llm.model:qwen3}")
    private String model;

    @Value("${ragflow.api.key}") // Added for new session APIs
    private String apiKey;

    @Value("${llm.timeout:120}")
    private int timeout;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper; // Added for JSON processing

    public ChatServiceImpl(RestTemplate restTemplate, DialogRepository dialogRepository,  DocumentRepository documentRepository, KnowledgebaseRepository knowledgebaseRepository) {
        this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper(); // Initialize ObjectMapper
        this.dialogRepository = dialogRepository;
        this.documentRepository = documentRepository;
        this.knowledgebaseRepository = knowledgebaseRepository;
    }

    public String createChatSession(String chatId, String question) throws RuntimeException {
        String url = ragflowBaseUrl + "/api/v1/chats/" + chatId + "/sessions";
        log.info("创建聊天会话 URL: {}", url);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", (question != null && !question.isEmpty()) ? 
            question.substring(0, Math.min(question.length(), 20)) : "New Session");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        log.info("发送创建会话请求:");
        log.info("  URL: {}", url);
        log.info("  请求体: {}", requestBody);
        log.info("  chatId: {}", chatId);

        try {
            ResponseEntity<CreateSessionInternalResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    CreateSessionInternalResponse.class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null && response.getBody().getData() != null) {
                String sessionId = response.getBody().getData().getId();
                if (sessionId == null || sessionId.isEmpty()) {
                    log.error("创建会话成功，但响应中未找到会话ID。响应: {}", response.getBody());
                    throw new RuntimeException("创建会话成功，但响应中未找到有效的会话ID");
                }
                log.info("聊天会话创建成功，会话ID: {}", sessionId);
                return sessionId;
            } else {
                log.error("创建聊天会话失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                throw new RuntimeException("创建聊天会话失败，状态码: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("创建聊天会话异常: {}", e.getMessage(), e);

            // 添加更详细的错误信息
            if (e instanceof org.springframework.web.client.HttpClientErrorException) {
                org.springframework.web.client.HttpClientErrorException httpError = (org.springframework.web.client.HttpClientErrorException) e;
                log.error("创建会话HTTP错误详情:");
                log.error("  状态码: {}", httpError.getStatusCode());
                log.error("  响应体: {}", httpError.getResponseBodyAsString());
                log.error("  请求URL: {}", url);
                log.error("  chatId: {}", chatId);

                if (httpError.getStatusCode() == HttpStatus.NOT_FOUND) {
                    log.error("chatId '{}' 不存在，请检查chatId是否正确", chatId);
                    throw new RuntimeException("chatId '" + chatId + "' 不存在，请检查chatId是否正确");
                }
            }
            throw new RuntimeException("创建聊天会话异常: " + e.getMessage(), e);
        }
    }

    public SessionChatResponse converseInSession(SessionChatRequest request, String chatId, String actualSessionId) {
        String url = ragflowBaseUrl + "/api/v1/chats/" + chatId + "/completions";
        log.info("会话内非流式聊天 URL: {}", url);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("question", request.getQuestion());
        requestBody.put("session_id", actualSessionId);
        requestBody.put("liveStyle", request.getLiveStyle());
        requestBody.put("context", request.getContext());

        requestBody.put("stream", false); // Explicitly false for this method

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        log.info("发送会话对话请求:");
        log.info("  URL: {}", url);
        log.info("  请求体: {}", requestBody);
        log.info("  请求头: {}", headers);

        try {
            // 先尝试获取原始响应
            ResponseEntity<String> rawResponse = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            log.info("原始响应状态码: {}", rawResponse.getStatusCode());
//            log.info("原始响应体: {}", rawResponse.getBody());

            if (rawResponse.getStatusCode() == HttpStatus.OK && rawResponse.getBody() != null) {
                try {
                    // 尝试解析为我们期望的格式
                    ConverseInternalResponse response = objectMapper.readValue(rawResponse.getBody(), ConverseInternalResponse.class);

                    if (response.getData() != null) {
                        SessionChatResponse chatResponse = new SessionChatResponse();

                        String answer = response.getData().getAnswer();
                        if (answer != null) {
                            // 去除 "#{数字}$!" 格式的字符串
                            answer = answer.replaceAll("##\\d\\$\\$", "");
                        }
                        chatResponse.setAnswer(answer);
                        chatResponse.setSessionId(response.getData().getSessionId());
                        chatResponse.setChatId(chatId);
                        chatResponse.setMessageId(response.getData().getId());
                        log.info("会话内非流式聊天成功，响应: {}", chatResponse);
                        return chatResponse;
                    } else {
                        log.error("响应中没有data字段");
                        throw new RuntimeException("响应格式错误：缺少data字段");
                    }
                } catch (Exception parseException) {
                    log.error("JSON解析失败: {}", parseException.getMessage());
                    log.error("尝试解析的JSON: {}", rawResponse.getBody());

                    // 如果解析失败，尝试作为通用Map解析
                    try {
                        Map<String, Object> genericResponse = objectMapper.readValue(rawResponse.getBody(), Map.class);
                        log.info("通用解析成功: {}", genericResponse);

                        // 尝试从通用格式中提取信息
                        SessionChatResponse chatResponse = new SessionChatResponse();

                        // 根据实际响应格式调整字段提取逻辑
                        if (genericResponse.containsKey("data")) {
                            Map<String, Object> data = (Map<String, Object>) genericResponse.get("data");
                            String answer = (String) data.get("answer");
                            if (answer != null) {
                                // 去除 "#{数字}$!" 格式的字符串
                                answer = answer.replaceAll("##\\d\\$\\$", "");
                            }
                            chatResponse.setAnswer(answer);
                            chatResponse.setSessionId(actualSessionId);
                            chatResponse.setChatId(chatId);
                            chatResponse.setMessageId((String) data.get("id"));
                        } else {
                            // 如果没有data字段，直接从根级别提取
                            String answer = (String) genericResponse.get("answer");
                            if (answer != null) {
                                // 去除 "#{数字}$!" 格式的字符串
                                answer = answer.replaceAll("##\\d\\$\\$", "");
                            }
                            chatResponse.setAnswer(answer);
                            chatResponse.setSessionId(actualSessionId);
                            chatResponse.setChatId(chatId);
                            chatResponse.setMessageId((String) genericResponse.get("id"));
                        }

                        return chatResponse;
                    } catch (Exception genericParseException) {
                        log.error("通用JSON解析也失败: {}", genericParseException.getMessage());
                        throw new RuntimeException("JSON解析失败: " + parseException.getMessage(), parseException);
                    }
                }
            } else {
                log.error("会话内非流式聊天失败，状态码: {}, 响应: {}", rawResponse.getStatusCode(), rawResponse.getBody());
                throw new RuntimeException("会话内非流式聊天失败，状态码: " + rawResponse.getStatusCode());
            }
        } catch (Exception e) {
            log.error("会话内非流式聊天异常: {}", e.getMessage(), e);

            // 添加更详细的错误信息
            if (e instanceof org.springframework.web.client.HttpClientErrorException) {
                org.springframework.web.client.HttpClientErrorException httpError = (org.springframework.web.client.HttpClientErrorException) e;
                log.error("HTTP错误详情:");
                log.error("  状态码: {}", httpError.getStatusCode());
                log.error("  响应体: {}", httpError.getResponseBodyAsString());
                log.error("  请求URL: {}", url);
                log.error("  chatId: {}", chatId);
                log.error("  sessionId: {}", actualSessionId);
            }

            throw new RuntimeException("会话内非流式聊天异常: " + e.getMessage(), e);
        }
    }

    @Override
    public KnowledgebaseEntity getKbByDatasetId(String datasetId) {
        KnowledgebaseEntity kbEntity = knowledgebaseRepository.findById(datasetId)
                .orElseGet(() -> {
                    log.warn("在数据库中未找到该知识库: {}", datasetId);
                    return null;
                });
        if (null != kbEntity) {
            return kbEntity;
        }
        return null;
    }

    @Override
    public String getChatIdByDatasetId(String datasetId) {
        // 根据 datasetId 查询 dialog 表获取 chatId
        DialogEntity dialogEntity = dialogRepository.findByKbIdsAndStatus("[\"" + datasetId + "\"]", "1")
                .orElseGet(() -> {
                    log.warn("在数据库中未找到该知识库对应的聊天助手: {}", datasetId);
                    return null;
                });
        if (null != dialogEntity) {
            return dialogEntity.getId();
        }
        return null;
    }

    public void converseInSessionStream(SessionChatRequest request, String chatId, String actualSessionId, SseEmitter emitter) {
        String urlString = ragflowBaseUrl + "/api/v1/chats/" + chatId + "/completions";
        log.info("会话内流式聊天 URL: {}", urlString);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("question", request.getQuestion());
        requestBody.put("session_id", actualSessionId);
        requestBody.put("liveStyle", request.getLiveStyle());
        requestBody.put("context", request.getContext());

        requestBody.put("stream", true);

        try {
            java.net.URL url = new java.net.URL(urlString);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            connection.setRequestProperty("Authorization", "Bearer " + apiKey);

            connection.setDoOutput(true);
            connection.setConnectTimeout(timeout * 1000);
            connection.setReadTimeout(timeout * 1000 * 5); // Longer read timeout for streams

            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = objectMapper.writeValueAsBytes(requestBody);
                os.write(input, 0, input.length);
                log.info("会话流式请求体已发送: {}", requestBody);
            }

            int responseCode = connection.getResponseCode();
            log.info("会话流式LLM API响应状态码: {}", responseCode);

            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("会话流式LLM API返回错误状态码: {}", responseCode);
                emitter.completeWithError(new RuntimeException("会话流式LLM API错误: " + responseCode + " " + connection.getResponseMessage()));
                return;
            }

            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug("会话流式收到原始行: {}", line);
                    if (line.startsWith("data:")) {
                        String jsonDataString = line.substring(5).trim();
                        if (jsonDataString.isEmpty() || "[DONE]".equalsIgnoreCase(jsonDataString)) { // Check for [DONE] or empty data
                            log.info("会话流式收到结束标记或空数据.");
                            // Send standard OpenAI [DONE] if this API doesn't send its own last chunk with finish_reason
                            Map<String, Object> doneChunk = new HashMap<>();
                            doneChunk.put("id", "chatcmpl-done-" + UUID.randomUUID().toString());
                            doneChunk.put("object", "chat.completion.chunk");
                            doneChunk.put("created", Instant.now().getEpochSecond());
                            doneChunk.put("model", chatId); // Or a more specific model if available
                            Map<String, Object> doneChoice = new HashMap<>();
                            doneChoice.put("index", 0);
                            doneChoice.put("delta", new HashMap<>()); // Empty delta
                            doneChoice.put("finish_reason", "stop");
                            doneChunk.put("choices", Arrays.asList(doneChoice));
                            emitter.send(SseEmitter.event().data(doneChunk));
                            break;
                        }

                        try {
                            // Assuming the data is {"code":0,"data":{"id": "...", "answer": "...", ...}}
                            Map<String, Object> outerMap = objectMapper.readValue(jsonDataString, Map.class);
                            if (outerMap.containsKey("data")) {
                                Map<String, Object> dataMap = (Map<String, Object>) outerMap.get("data");
                                String answerChunk = (String) dataMap.get("answer");
                                String messageId = (String) dataMap.get("id"); // This is likely the message/turn ID

                                if (answerChunk != null && !answerChunk.isEmpty()) {
                                    Map<String, Object> streamChunk = new HashMap<>();
                                    streamChunk.put("id", messageId != null ? messageId : "chatcmpl-sess-" + UUID.randomUUID().toString());
                                    streamChunk.put("object", "chat.completion.chunk");
                                    streamChunk.put("created", Instant.now().getEpochSecond());
                                    // Determine model for response, could be from request or a default
                                    streamChunk.put("model", model);

                                    Map<String, Object> streamChoice = new HashMap<>();
                                    streamChoice.put("index", 0);
                                    Map<String, String> streamDelta = new HashMap<>();
                                    streamDelta.put("content", answerChunk);
                                    streamChoice.put("delta", streamDelta);
                                    // The API doc doesn't specify finish_reason in stream. Assuming null until last message.
                                    streamChoice.put("finish_reason", null);

                                    streamChunk.put("choices", Arrays.asList(streamChoice));
                                    emitter.send(SseEmitter.event().data(streamChunk));
                                    log.debug("转发会话流式内容: {}", answerChunk);
                                }
                                // Check if this custom API signals end of stream within its data structure
                                // e.g. if dataMap.get("total") != null or some other field indicates completion
                                // If so, send the final OpenAI chunk with finish_reason: "stop"
                                // For now, relying on stream closure or explicit [DONE]
                            } else {
                                // If data doesn't have "data" field, maybe it's a different event type or an error
                                log.warn("会话流式收到的数据格式不符合预期 (缺少 'data' 字段): {}", jsonDataString);
                                // Optionally forward raw if it's an unexpected format but not an error
                                // emitter.send(SseEmitter.event().data(objectMapper.readValue(jsonDataString, Map.class)));
                            }
                        } catch (Exception e) {
                            log.warn("解析会话流式数据失败: {}, 原始数据: {}", e.getMessage(), jsonDataString);
                            // Forward raw data on parse error, ensuring it's a valid JSON object for SSE
                            try {
                                emitter.send(SseEmitter.event().data(objectMapper.readValue(jsonDataString, Map.class)));
                            } catch (Exception ex) {
                                log.warn("无法将原始数据作为JSON转发: {}", ex.getMessage());
                            }
                        }
                    }
                }
                log.info("会话流式响应处理完成.");
                emitter.complete();
            }

        } catch (MalformedURLException e) {
            log.error("会话流式URL格式错误: {}", urlString, e);
            emitter.completeWithError(e);
        } catch (IOException e) {
            log.error("会话流式IO异常: {}", e.getMessage(), e);
            emitter.completeWithError(e);
        } catch (Exception e) { // Catch any other unexpected errors
            log.error("会话流式处理时发生未知异常: {}", e.getMessage(), e);
            emitter.completeWithError(e);
        }
    }

    /**
     * 处理通用聊天请求
     */
    public ChatResponse processChat(ChatRequest request) {
        try {
            log.info("处理聊天请求: {}", request);

            // 调用真实的LLM模型
            return callRealLLM(request);
        } catch (Exception e) {
            log.error("处理聊天请求失败", e);
            // 如果真实调用失败，降级到模拟响应
            log.warn("降级到模拟响应");
            return buildMockResponse(request);
        }
    }

    /**
     * 模拟流式响应（降级方案）
     */
    private void simulateStreamResponse(ChatRequest request, SseEmitter emitter) throws IOException {
        String userMessage = extractUserMessage(request);
        String fullResponse = "抱歉，真实LLM流式调用失败，这是模拟的流式响应。您的问题是：\"" + userMessage + "\"。我会逐字返回这个回复来模拟流式效果。";

        // 模拟逐字返回
        for (int i = 0; i < fullResponse.length(); i++) {
            try {
                Map<String, Object> chunk = new HashMap<>();
                chunk.put("id", "mock-" + UUID.randomUUID().toString());
                chunk.put("object", "chat.completion.chunk");
                chunk.put("created", Instant.now().getEpochSecond());
                chunk.put("model", model);

                Map<String, Object> choice = new HashMap<>();
                choice.put("index", 0);
                Map<String, String> delta = new HashMap<>();
                delta.put("content", String.valueOf(fullResponse.charAt(i)));
                choice.put("delta", delta);
                choice.put("finish_reason", null);

                chunk.put("choices", Arrays.asList(choice));

                emitter.send(SseEmitter.event().data(chunk));

                // 模拟打字效果
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        // 发送结束标记
        Map<String, Object> endChunk = new HashMap<>();
        endChunk.put("id", "mock-" + UUID.randomUUID().toString());
        endChunk.put("object", "chat.completion.chunk");
        endChunk.put("created", Instant.now().getEpochSecond());
        endChunk.put("model", model);

        Map<String, Object> endChoice = new HashMap<>();
        endChoice.put("index", 0);
        endChoice.put("delta", new HashMap<>());
        endChoice.put("finish_reason", "stop");

        endChunk.put("choices", Arrays.asList(endChoice));
        emitter.send(SseEmitter.event().data(endChunk));

        emitter.complete();
    }

    /**
     * 调用真实的LLM模型（非流式）
     */
    private ChatResponse callRealLLM(ChatRequest request) {
        String url = baseUrl + "/chat/completions";

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);
        requestBody.put("messages", convertMessages(request.getMessages()));
        requestBody.put("stream", false); // 非流式
        // 添加chat_template_kwargs参数（内部使用，不暴露给Controller）
        Map<String, Object> chatTemplateKwargs = new HashMap<>();
        chatTemplateKwargs.put("enable_thinking", true);
        requestBody.put("chat_template_kwargs", chatTemplateKwargs);

        if (request.getMaxTokens() != null) {
            requestBody.put("max_tokens", request.getMaxTokens());
        }
        if (request.getTemperature() != null) {
            requestBody.put("temperature", request.getTemperature());
        }
        if (request.getTopP() != null) {
            requestBody.put("top_p", request.getTopP());
        }

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        log.info("调用LLM API: {}", url);
        log.info("请求体: {}", requestBody);

        try {
            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    Map.class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return convertToStandardResponse(response.getBody());
            } else {
                throw new RuntimeException("LLM API调用失败，状态码: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("调用LLM API异常: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 转换消息格式
     */
    private List<Map<String, String>> convertMessages(List<ChatRequest.ChatMessage> messages) {
        List<Map<String, String>> convertedMessages = new ArrayList<>();

        if (messages != null) {
            for (ChatRequest.ChatMessage msg : messages) {
                Map<String, String> message = new HashMap<>();
                message.put("role", msg.getRole());
                message.put("content", msg.getContent());
                convertedMessages.add(message);
            }
        }

        return convertedMessages;
    }

    /**
     * 转换API响应为标准格式
     */
    private ChatResponse convertToStandardResponse(Map<String, Object> apiResponse) {
        ChatResponse response = new ChatResponse();

        // 基本信息
        response.setId((String) apiResponse.get("id"));
        response.setObject((String) apiResponse.get("object"));
        response.setCreated(((Number) apiResponse.get("created")).longValue());
        response.setModel((String) apiResponse.get("model"));

        // 选择项
        List<Map<String, Object>> choices = (List<Map<String, Object>>) apiResponse.get("choices");
        if (choices != null && !choices.isEmpty()) {
            List<ChatResponse.Choice> responseChoices = new ArrayList<>();

            for (Map<String, Object> choice : choices) {
                ChatResponse.Choice responseChoice = new ChatResponse.Choice();
                responseChoice.setIndex(((Number) choice.get("index")).intValue());
                responseChoice.setFinishReason((String) choice.get("finish_reason"));

                // 消息
                Map<String, Object> message = (Map<String, Object>) choice.get("message");
                if (message != null) {
                    ChatRequest.ChatMessage responseMessage = new ChatRequest.ChatMessage();
                    responseMessage.setRole((String) message.get("role"));
                    responseMessage.setContent((String) message.get("content"));
                    responseChoice.setMessage(responseMessage);
                }

                responseChoices.add(responseChoice);
            }

            response.setChoices(responseChoices);
        }

        // 使用情况
        Map<String, Object> usage = (Map<String, Object>) apiResponse.get("usage");
        if (usage != null) {
            ChatResponse.Usage responseUsage = new ChatResponse.Usage();
            responseUsage.setPromptTokens(((Number) usage.get("prompt_tokens")).intValue());
            responseUsage.setCompletionTokens(((Number) usage.get("completion_tokens")).intValue());
            responseUsage.setTotalTokens(((Number) usage.get("total_tokens")).intValue());
            response.setUsage(responseUsage);
        }

        return response;
    }

    /**
     * 构建模拟响应（降级方案）
     */
    private ChatResponse buildMockResponse(ChatRequest request) {
        ChatResponse response = new ChatResponse();
        response.setId(UUID.randomUUID().toString());
        response.setObject("chat.completion");
        response.setCreated(Instant.now().getEpochSecond());
        response.setModel(model);

        // 构建选择项
        ChatResponse.Choice choice = new ChatResponse.Choice();
        choice.setIndex(0);
        choice.setFinishReason("stop");

        ChatRequest.ChatMessage message = new ChatRequest.ChatMessage();
        message.setRole("assistant");

        // 根据用户输入生成简单的响应
        String userMessage = extractUserMessage(request);
        String responseContent = generateResponse(userMessage);
        message.setContent(responseContent);
        choice.setMessage(message);

        response.setChoices(Arrays.asList(choice));

        // 构建使用情况
        ChatResponse.Usage usage = new ChatResponse.Usage();
        usage.setPromptTokens(calculateTokens(userMessage));
        usage.setCompletionTokens(calculateTokens(responseContent));
        usage.setTotalTokens(usage.getPromptTokens() + usage.getCompletionTokens());
        response.setUsage(usage);

        return response;
    }

    /**
     * 提取用户消息
     */
    private String extractUserMessage(ChatRequest request) {
        if (request.getMessages() != null && !request.getMessages().isEmpty()) {
            for (ChatRequest.ChatMessage msg : request.getMessages()) {
                if ("user".equals(msg.getRole())) {
                    return msg.getContent();
                }
            }
        }
        return "你好";
    }

    /**
     * 生成简单响应
     */
    private String generateResponse(String userMessage) {
        return "抱歉，真实LLM调用失败，这是降级响应。您的问题是：\"" + userMessage + "\"";
    }

    /**
     * 简单的token计算
     */
    private Integer calculateTokens(String text) {
        if (text == null) return 0;
        // 简单估算：中文字符按2个token计算，英文单词按1个token计算
        int chineseChars = text.replaceAll("[^\\u4e00-\\u9fa5]", "").length();
        int englishWords = text.replaceAll("[\\u4e00-\\u9fa5]", "").split("\\s+").length;
        return chineseChars * 2 + englishWords;
    }

    /**
     * 调用真实的LLM模型（流式）
     */
    public void callCommonLLMStream(ChatRequest request, SseEmitter emitter) {
        String url = baseUrl + "/chat/completions";

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);
        requestBody.put("messages", convertMessages(request.getMessages()));
        requestBody.put("stream", true); // 真正的流式

        if (request.getMaxTokens() != null) {
            requestBody.put("max_tokens", request.getMaxTokens());
        }
        if (request.getTemperature() != null) {
            requestBody.put("temperature", request.getTemperature());
        }
        if (request.getTopP() != null) {
            requestBody.put("top_p", request.getTopP());
        }

        log.info("调用LLM流式API: {}", url);
        log.info("流式请求体: {}", requestBody);

        try {
            // 使用原生HTTP连接处理流式响应
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) urlObj.openConnection();

            // 设置请求属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);
            connection.setConnectTimeout(timeout * 1000);
            connection.setReadTimeout(timeout * 1000);

            // 发送请求体
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsBytes(requestBody);
                os.write(input, 0, input.length);
                log.info("请求体已发送");
            }

            // 检查响应状态
            int responseCode = connection.getResponseCode();
            log.info("LLM API响应状态码: {}", responseCode);

            if (responseCode != 200) {
                log.error("LLM API返回错误状态码: {}", responseCode);
                emitter.completeWithError(new RuntimeException("LLM API错误: " + responseCode));
                return;
            }

            // 读取流式响应
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(connection.getInputStream(), "UTF-8"))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug("收到原始行: {}", line);

                    if (line.startsWith("data: ")) {
                        String data = line.substring(6); // 去掉 "data: " 前缀

                        // 检查是否是结束标记
                        if ("[DONE]".equals(data.trim())) {
                            log.info("收到结束标记");
                            emitter.send(SseEmitter.event().data("[DONE]"));
                            break;
                        }

                        // 解析JSON并提取content字段
                        try {
                            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                            Map<String, Object> jsonData = mapper.readValue(data, Map.class);

                            List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonData.get("choices");
                            if (choices != null && !choices.isEmpty()) {
                                Map<String, Object> choice = choices.get(0);
                                Map<String, Object> delta = (Map<String, Object>) choice.get("delta");

                                if (delta != null) {
                                    String content = (String) delta.get("content");
                                    // 只转发有实际content内容的数据，忽略reasoning_content
                                    if (content != null && !content.isEmpty()) {
                                        // 构建标准的OpenAI流式响应格式
                                        Map<String, Object> streamChunk = new HashMap<>();
                                        streamChunk.put("id", jsonData.get("id"));
                                        streamChunk.put("object", "chat.completion.chunk");
                                        streamChunk.put("created", jsonData.get("created"));
                                        streamChunk.put("model", jsonData.get("model"));

                                        Map<String, Object> streamChoice = new HashMap<>();
                                        streamChoice.put("index", 0);
                                        streamChoice.put("finish_reason", choice.get("finish_reason"));

                                        Map<String, String> streamDelta = new HashMap<>();
                                        streamDelta.put("content", content);
                                        streamChoice.put("delta", streamDelta);

                                        streamChunk.put("choices", Arrays.asList(streamChoice));

                                        emitter.send(SseEmitter.event().data(streamChunk));
                                        log.debug("转发流式内容: {}", content);
                                    }

                                    // 处理结束标记
                                    String finishReason = (String) choice.get("finish_reason");
                                    if ("length".equals(finishReason) || "stop".equals(finishReason)) {
                                        log.info("收到完成标记: {}", finishReason);
                                        // 发送结束chunk
                                        Map<String, Object> endChunk = new HashMap<>();
                                        endChunk.put("id", jsonData.get("id"));
                                        endChunk.put("object", "chat.completion.chunk");
                                        endChunk.put("created", jsonData.get("created"));
                                        endChunk.put("model", jsonData.get("model"));

                                        Map<String, Object> endChoice = new HashMap<>();
                                        endChoice.put("index", 0);
                                        endChoice.put("delta", new HashMap<>());
                                        endChoice.put("finish_reason", finishReason);

                                        endChunk.put("choices", Arrays.asList(endChoice));
                                        endChunk.put("usage", jsonData.get("usage"));

                                        emitter.send(SseEmitter.event().data(endChunk));
                                        emitter.send(SseEmitter.event().data("[DONE]"));
                                        break;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.warn("解析流式数据失败: {}, 原始数据: {}", e.getMessage(), data);
                            // 如果解析失败，直接转发原始数据
                            emitter.send(SseEmitter.event().data(data));
                        }
                    }
                }
            }

            log.info("流式响应处理完成");
            emitter.complete();

        } catch (Exception e) {
            log.error("流式LLM API调用异常: {}", e.getMessage(), e);
            // 降级到模拟流式响应
            try {
                log.warn("降级到模拟流式响应");
                simulateStreamResponse(request, emitter);
            } catch (IOException ioException) {
                emitter.completeWithError(ioException);
            }
        }
    }

    @Override
    public LiveCommentResponse processLiveComment(LiveCommentRequest request) {
        try {
            log.info("开始处理直播间弹幕问题: question={}, datasetId={}, livePoints={}", 
                    request.getQuestion(), request.getDatasetId(), request.getLivePoints());

            KnowledgebaseEntity database = this.getKbByDatasetId(request.getDatasetId());
            if (null == database) {
                throw new RuntimeException("该知识库ID: " + request.getDatasetId() + "不存在或有误");
            }

            // 根据知识库ID获取chatId
            String chatId = getChatIdByDatasetId(request.getDatasetId());
            if (chatId == null || chatId.trim().isEmpty()) {
                throw new RuntimeException("无法根据知识库ID找到对应的聊天助手配置");
            }

            //先查找知识库的qa文档中是否包含对应qa
            String answer = "";
            String sessionId = request.getSessionId();
            Boolean isMatchQa = false;
            String qaId = "";
            List<String> documentIds = documentRepository.getDocumentIdByDatasetId(request.getDatasetId());
            if(!documentIds.isEmpty()){
                QADataQueryRequest qaDataQueryRequest = new QADataQueryRequest();
                qaDataQueryRequest.setQuestion(request.getQuestion());
                List<String> dataSets = new ArrayList<>();
                dataSets.add(request.getDatasetId());
                qaDataQueryRequest.setDataset_ids(dataSets);
                qaDataQueryRequest.setDocument_ids(documentIds);
                qaDataQueryRequest.setSimilarity_Threshold(0.1);
                qaDataQueryRequest.setVector_similarity_weight(0.5);
                qaDataQueryRequest.setRerank_id("bge-reranker-large@Xinference");

                RagFlowResponse<?> qaResponse =  createQAanalyse(qaDataQueryRequest);
                Map<String,Object> qaData =  (Map<String,Object>)qaResponse.getData();
                if(qaData != null && !qaData.isEmpty()){
                    List<Map<String,String>> content = (List<Map<String, String>>) qaData.get("chunks");
                    if (content != null && !content.isEmpty()) {
                        String content1 = content.get(0).get("content");
                        qaId = content.get(0).get("id");
                        Object similarityObj = content.get(0).get("vector_similarity");

                        Double similarity = (Double) similarityObj;
                        if(similarity > 0.90){
                            answer = ExtractAnswerFromQaChunk(content1);
                            isMatchQa = true;
                        }
                    }
                }
            }
            if(!isMatchQa) {
                if (!StringUtils.hasText(sessionId)) {
                    // 创建临时会话处理弹幕问题
                    sessionId = createChatSession(chatId, request.getQuestion());
                }

                // 构建SessionChatRequest
                SessionChatRequest sessionRequest = new SessionChatRequest();
                sessionRequest.setDatasetId(request.getDatasetId());
                sessionRequest.setSessionId(sessionId);
                sessionRequest.setQuestion(request.getQuestion());
                sessionRequest.setStream(false);
                sessionRequest.setLiveStyle(getLiveCommentStyle());
                sessionRequest.setContext(getLiveCommentContext(database.getDescription(), request.getExtraInfo()));

                // 调用会话处理方法
                SessionChatResponse sessionResponse = converseInSession(sessionRequest, chatId, sessionId);

                // 解析响应内容，提取脚本和定位信息
                answer = sessionResponse.getAnswer();
            }
            return parseLiveCommentResponse(request.getQuestion(), answer, request.getLivePoints(), isMatchQa, qaId, sessionId);

        } catch (Exception e) {
            log.error("处理直播间弹幕问题失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理直播间弹幕问题失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从QA对中提取回答
     * @param content 内容
     * @return 回答
     */
    private String ExtractAnswerFromQaChunk(String content) {
        try {
            if (content == null || content.trim().isEmpty()) {
                log.warn("待提取的QA对内容为空!");
                return null;
            }

            // 清理HTML标签
            String cleanContent = removeHtmlTags(content);

            String question = "";
            String answer = "";

            // 尝试多种格式解析
            boolean parsed = false;

            // 格式1: "Question: xxx\tAnswer: yyy" (标准格式) 或 "问题：xxx\t回答：yyy"
            if (!parsed && cleanContent.contains("\t")) {
                String[] parts = cleanContent.split("\t", 2);
                if (parts.length == 2) {
                    String questionPart = parts[0].trim();
                    String answerPart = parts[1].trim();

                    if (questionPart.toLowerCase().startsWith("question:")) {
                        question = questionPart.substring(questionPart.indexOf(":") + 1).trim();
                        if (answerPart.toLowerCase().startsWith("answer:")) {
                            answer = answerPart.substring(answerPart.indexOf(":") + 1).trim();
                            parsed = true;
                        }
                    } else if (questionPart.toLowerCase().startsWith("问题：")) {
                        question = questionPart.substring(questionPart.indexOf("：") + 1).trim();
                        if (answerPart.toLowerCase().startsWith("回答：")) {
                            answer = answerPart.substring(answerPart.indexOf("：") + 1).trim();
                            parsed = true;
                        }
                    }

                }
            }
            // 格式2: "question:xxx answer:yyy" (空格分隔)
            if (!parsed) {
                // 使用正则表达式匹配 question:...answer:... 格式
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                        "question\\s*:\\s*(.*?)\\s+answer\\s*:\\s*(.*)",
                        java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
                );
                java.util.regex.Matcher matcher = pattern.matcher(cleanContent);
                if (matcher.find()) {
                    question = matcher.group(1).trim();
                    answer = matcher.group(2).trim();
                    parsed = true;
                }
            }
            // 格式3: "问题：xxx 回答：yyy" (空格分隔)
            if (!parsed) {
                // 使用正则表达式匹配 问题：...回答：... 格式
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                        "问题\\s*：\\s*(.*?)\\s+回答\\s*：\\s*(.*)",
                        java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
                );
                java.util.regex.Matcher matcher = pattern.matcher(cleanContent);
                if (matcher.find()) {
                    question = matcher.group(1).trim();
                    answer = matcher.group(2).trim();
                    parsed = true;
                }
            }

            // 格式4: "question{内容}answer{内容}" (直接拼接格式)
            if (!parsed) {
                // 使用正则表达式匹配 question...answer... 格式（无分隔符）
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                        "question(.*?)answer(.*)",
                        java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
                );
                java.util.regex.Matcher matcher = pattern.matcher(cleanContent);
                if (matcher.find()) {
                    question = matcher.group(1).trim();
                    answer = matcher.group(2).trim();
                    parsed = true;
                }
            }

            // 格式5: 尝试其他可能的分隔符
            if (!parsed) {
                // 尝试换行符分隔
                String[] lines = cleanContent.split("\\r?\\n");
                if (lines.length >= 2) {
                    for (int i = 0; i < lines.length - 1; i++) {
                        String line1 = lines[i].trim();
                        String line2 = lines[i + 1].trim();

                        if (line1.toLowerCase().startsWith("question:") && line2.toLowerCase().startsWith("answer:")) {
                            question = line1.substring(line1.indexOf(":") + 1).trim();
                            answer = line2.substring(line2.indexOf(":") + 1).trim();
                            parsed = true;
                            break;
                        }
                    }
                }
            }

            if (!parsed && !content.isEmpty()) {
                log.warn("QA对内容格式异常，content: {}", content);
                question = content;
                answer = "";
                parsed = true;
            }

            // 如果所有格式都解析失败，记录警告并返回null
            if (!parsed || question.isEmpty() || answer.isEmpty()) {
                log.warn("QA对内容格式无法解析，content: {}", content);
                return null;
            }

            return answer;

        } catch (Exception e) {
            log.error("转换QA对时发生异常！", e);
            return null;
        }
    }

    /**
     * 移除HTML标签
     */
    private String removeHtmlTags(String content) {
        if (content == null) {
            return null;
        }
        // 移除HTML标签，但保留文本内容
        return content.replaceAll("<[^>]+>", "");
    }

    /**
     * 构建直播间弹幕问题的特殊提示词
     */
    public String getLiveCommentStyle() {
        return "- **【核心要求】: 简短、快速、互动感强。**\n" +
                "- **【字数限制】**: 你的回答【必须】非常简短精悍，**理想情况下不超过50个字**。这用于直播中的实时快速问答。\n" +
                "- **【直击要点】**: 直接给出问题的核心答案，**不要做任何多余的铺垫或扩展解释**。就像在飞速闪过的弹幕里挑出一条来秒回，语气要非常自然、即时。\n" +
                "- **【互动要求】**: \n" +
                "   - 自然地使用“宝子们”、“家人们”、“我跟你说”等词语；\n" +
                "   - 当你需要引导观众进行点赞、关注或点击特定按钮等互动行为时，【必须统一使用】“点点小风车”这个说法。严禁使用“点赞”、“关注”、“小红星”等其他通用话术。示例: \"喜欢这款车的宝子们，右下角小风车给我点起来！\"、\"还没点小风车的抓紧时间哈！\"";
    }

    /**
     * 构建直播间弹幕问题的上下文
     */
    public String getLiveCommentContext(String kbDescription, String userContext) {
        return String.format("当前时间: %s" + (StringUtils.hasText(kbDescription) ? "\n" + kbDescription : "") + (StringUtils.hasText(userContext) ? "\n" + userContext : ""),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }

    /**
     * 构建预置脚本生成的特殊提示词
     */
    public String getPresetScriptStyle() {
        return "- **【核心要求】: 详尽、丰富、有吸引力。**\n" +
                "- **【无字数限制】**: 你可以生成一段内容丰富、细节详实的完整解说词，适合作为直播前的预置脚本或长段口播稿。\n" +
                "- **【结构化介绍】**: 你的回答应该有逻辑、有层次。可以从不同角度（例如：先说外观设计、再讲内饰配置、然后谈动力性能）来组织介绍，让内容听起来有条理、有深度。\n" +
                "- **【营造期待】**: 在介绍中可以加入一些引子和悬念，比如“这个功能我跟你们说，简直了！”，以激发观众的好奇心和购买欲。\n" +
                "- **【互动要求】**: \n" +
                "   - 自然地使用“宝子们”、“家人们”、“我跟你说”等词语；\n" +
                "   - 当你需要引导观众进行点赞、关注或点击特定按钮等互动行为时，【必须统一使用】“点点小风车”这个说法。严禁使用“点赞”、“关注”、“小红星”等其他通用话术。示例: \"喜欢这款车的宝子们，右下角小风车给我点起来！\"、\"还没点小风车的抓紧时间哈！\"";
    }

    /**
     * 构建预置脚本生成的上下文
     */
    public String getPresetScriptContext(String kbDescription, String userContext) {
        return String.format("当前时间: %s" + (StringUtils.hasText(kbDescription) ? "\n" + kbDescription : "") + (StringUtils.hasText(userContext) ? "\n" + userContext : ""),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }

    /**
     * 解析直播间弹幕回答，提取脚本和定位信息
     */
    private LiveCommentResponse parseLiveCommentResponse(String question, String responseContent, List<String> livePoints, Boolean isMatchQa, String qaId, String sessionId) {
        if (responseContent == null || responseContent.trim().isEmpty()) {
            return new LiveCommentResponse("抱歉，暂时无法为您介绍这个内容，咱们来看看其他精彩的地方吧！", 
                    livePoints.isEmpty() ? "" : livePoints.get(0), false, isMatchQa ? qaId : null, sessionId);
        }
        return new LiveCommentResponse(cleanupScript(responseContent), findBestMatchingLocation(question, livePoints), isMatchQa, isMatchQa ? qaId : null, sessionId);
    }

    /**
     * 检查livePointsResult字符串是否包含任何可选的直播点位
     */
    private boolean containsAnyLivePoint(String livePointsResult, List<String> livePoints) {
        if (livePointsResult == null || livePointsResult.trim().isEmpty()) {
            return false;
        }
        
        // 处理逗号分隔的多个部位
        String[] parts = livePointsResult.split(",");
        for (String part : parts) {
            String trimmedPart = part.trim();
            if (livePoints.contains(trimmedPart)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从响应中提取JSON内容
     */
    private String extractJsonFromResponse(String responseContent) {
        // 尝试提取```json代码块中的内容
        java.util.regex.Pattern jsonBlockPattern = java.util.regex.Pattern.compile("```json\\s*\\n([\\s\\S]*?)\\n```");
        java.util.regex.Matcher jsonBlockMatcher = jsonBlockPattern.matcher(responseContent);
        if (jsonBlockMatcher.find()) {
            return jsonBlockMatcher.group(1).trim();
        }
        
        // 尝试提取```代码块中的内容
        java.util.regex.Pattern codeBlockPattern = java.util.regex.Pattern.compile("```\\s*\\n([\\s\\S]*?)\\n```");
        java.util.regex.Matcher codeBlockMatcher = codeBlockPattern.matcher(responseContent);
        if (codeBlockMatcher.find()) {
            String content = codeBlockMatcher.group(1).trim();
            if (content.startsWith("{") && content.endsWith("}")) {
                return content;
            }
        }
        
        // 尝试提取反引号包围的JSON
        java.util.regex.Pattern backtickPattern = java.util.regex.Pattern.compile("`([^`]*\\{[^`]*\\}[^`]*)`");
        java.util.regex.Matcher backtickMatcher = backtickPattern.matcher(responseContent);
        if (backtickMatcher.find()) {
            return backtickMatcher.group(1).trim();
        }
        
        // 直接尝试提取JSON对象
        java.util.regex.Pattern directJsonPattern = java.util.regex.Pattern.compile("\\{[^{}]*\"script\"[^{}]*\"livePoints\"[^{}]*\\}");
        java.util.regex.Matcher directJsonMatcher = directJsonPattern.matcher(responseContent);
        if (directJsonMatcher.find()) {
            return directJsonMatcher.group().trim();
        }
        
        return null;
    }

    /**
     * 通过关键词匹配找到最佳位置
     */
    private String findBestMatchingLocation(String content, List<String> livePoints) {
        // 创建点位匹配优先级，优先匹配更具体的关键词
        java.util.Map<String, Integer> pointScores = new java.util.HashMap<>();
        
        for (String point : livePoints) {
            int score = 0;
            String lowerContent = content.toLowerCase();
            String lowerPoint = point.toLowerCase();
            
            // 直接匹配点位名称
            if (lowerContent.contains(lowerPoint)) {
                score += 10;
            }
            
            // 根据不同点位的相关关键词进行匹配
            score += getLocationKeywordScore(lowerContent, point);
            
            if (score > 0) {
                pointScores.put(point, score);
            }
        }
        
        // 选择得分最高的点位
        if (!pointScores.isEmpty()) {
            return pointScores.entrySet().stream()
                .max(java.util.Map.Entry.comparingByValue())
                .map(java.util.Map.Entry::getKey)
                .orElse("");
        }
        
        return "";
    }

    /**
     * 清理脚本内容，确保自然性
     */
    private String cleanupScript(String script) {
        if (script == null) {
            return "";
        }
        
        // 移除可能的格式标记
        script = script.replaceAll("\\[点位:[^\\]]+\\]", "").trim();
        
        // 移除多余的换行和空格
        script = script.replaceAll("\\n+", " ").replaceAll("\\s+", " ").trim();
        
        // 确保以自然的语调结尾
        if (!script.isEmpty() && !script.endsWith("！") && !script.endsWith("。") && 
            !script.endsWith("~") && !script.endsWith("呢") && !script.endsWith("哦")) {
            script += "！";
        }
        
        return script;
    }

    /**
     * 根据内容和点位计算关键词匹配得分
     */
    private int getLocationKeywordScore(String content, String pointName) {
        int score = 0;
        
        // 根据不同点位定义相关关键词
        switch (pointName.toLowerCase()) {
            case "车头":
                if (content.contains("车头") || content.contains("前脸") || content.contains("大灯") || 
                    content.contains("进气格栅") || content.contains("雾灯") || content.contains("前保险杠") ||
                    content.contains("引擎盖") || content.contains("前部")) {
                    score += 5;
                }
                break;
            case "车身":
                if (content.contains("车身") || content.contains("侧面") || content.contains("车门") || 
                    content.contains("侧窗") || content.contains("车窗") || content.contains("腰线") ||
                    content.contains("轮毂") || content.contains("轮胎") || content.contains("侧裙") ||
                    content.contains("后视镜")) {
                    score += 5;
                }
                break;
            case "车尾":
                if (content.contains("车尾") || content.contains("尾灯") || content.contains("后备箱") ||
                    content.contains("后保险杠") || content.contains("排气管") || content.contains("尾部") ||
                    content.contains("后窗")) {
                    score += 5;
                }
                break;
        }
        
        return score;
    }

    //在qa数据集中查询对应qa对
    public RagFlowResponse<?> createQAanalyse(QADataQueryRequest request) {
        String url = ragflowBaseUrl + "/api/v1/retrieval";
        HttpEntity<QADataQueryRequest> entity = new HttpEntity<>(request, getHeaders());

        log.info("QA文档查询请求: {}", request);
        try {
            RagFlowResponse<?> response = restTemplate.postForObject(url, entity, RagFlowResponse.class);

            // 检查并处理实际的错误情况
            if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
                String dataStr = (String) response.getData();
                if (dataStr != null && !dataStr.isEmpty()) {
                    log.warn("QA文档查询操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                    response.setCode(-1); // 设置为通用错误码
                    response.setMessage(dataStr); // 将错误信息设置到 message 字段
                    return response;
                }
            }

            log.info("QA文档查询成功，响应: {}", response);
            return response;
        } catch (HttpClientErrorException e) {
            log.error("QA文档查询失败 (HTTP {}): {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString(), request, e);
            return new RagFlowResponse<>(-1, "QA文档查询失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("QA文档查询时发生未知错误: {}, Body: {}", e.getMessage(), request, e);
            return new RagFlowResponse<>(-1, "QA查询文档时发生未知错误: " + e.getMessage(), null);
        }
    }

    /**
     * 获取请求头
     */
    private HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);
        return headers;
    }
}
