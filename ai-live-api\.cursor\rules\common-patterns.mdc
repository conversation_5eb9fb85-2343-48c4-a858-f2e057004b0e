---
description: 
globs: 
alwaysApply: false
---
# 常用模式和模板

## API响应模式

### 统一响应格式
使用 [ApiResponse.java](mdc:src/main/java/com/unnet/api/dto/ApiResponse.java) 包装所有API响应：

```java
// 成功响应
return ApiResponse.success(data);

// 成功响应带消息
return ApiResponse.success(data, "操作成功");

// 失败响应
return ApiResponse.error("操作失败");

// 失败响应带错误码
return ApiResponse.error(ErrorCode.BUSINESS_ERROR, "业务错误");
```

## 控制器模式

### 标准Controller模板
```java
@RestController
@RequestMapping("/api/v1/resource")
@Api(tags = "资源管理API")
@Validated
@Slf4j
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取资源")
    public ApiResponse<ResourceResponse> getById(@PathVariable Long id) {
        ResourceResponse response = resourceService.getById(id);
        return ApiResponse.success(response);
    }

    @PostMapping
    @ApiOperation("创建资源")
    public ApiResponse<ResourceResponse> create(@Valid @RequestBody ResourceRequest request) {
        ResourceResponse response = resourceService.create(request);
        return ApiResponse.success(response, "创建成功");
    }

    @PutMapping("/{id}")
    @ApiOperation("更新资源")
    public ApiResponse<ResourceResponse> update(@PathVariable Long id, 
                                              @Valid @RequestBody ResourceRequest request) {
        ResourceResponse response = resourceService.update(id, request);
        return ApiResponse.success(response, "更新成功");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除资源")
    public ApiResponse<Void> delete(@PathVariable Long id) {
        resourceService.delete(id);
        return ApiResponse.success(null, "删除成功");
    }
}
```

## 服务层模式

### Service接口模板
```java
public interface ResourceService {
    ResourceResponse getById(Long id);
    ResourceResponse create(ResourceRequest request);
    ResourceResponse update(Long id, ResourceRequest request);
    void delete(Long id);
    List<ResourceResponse> list(ResourceQuery query);
}
```

### Service实现模板
```java
@Service
@Slf4j
public class ResourceServiceImpl implements ResourceService {

    @Override
    public ResourceResponse getById(Long id) {
        log.info("获取资源详情，ID: {}", id);
        
        // 参数校验
        if (id == null || id <= 0) {
            throw new BusinessException("资源ID不能为空");
        }
        
        // 业务逻辑
        // ...
        
        return response;
    }

    @Override
    @Transactional
    public ResourceResponse create(ResourceRequest request) {
        log.info("创建资源，请求参数: {}", request);
        
        try {
            // 业务逻辑
            // ...
            
            log.info("资源创建成功，ID: {}", response.getId());
            return response;
        } catch (Exception e) {
            log.error("创建资源失败", e);
            throw new BusinessException("创建资源失败");
        }
    }
}
```

## DTO模式

### Request DTO模板
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("资源创建请求")
public class ResourceRequest {

    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称长度不能超过100个字符")
    @ApiModelProperty(value = "资源名称", required = true, example = "示例资源")
    private String name;

    @NotBlank(message = "描述不能为空")
    @Length(max = 500, message = "描述长度不能超过500个字符")
    @ApiModelProperty(value = "资源描述", required = true)
    private String description;

    @NotNull(message = "类型不能为空")
    @ApiModelProperty(value = "资源类型", required = true)
    private ResourceType type;
}
```

### Response DTO模板
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("资源响应")
public class ResourceResponse {

    @ApiModelProperty("资源ID")
    private Long id;

    @ApiModelProperty("资源名称")
    private String name;

    @ApiModelProperty("资源描述")
    private String description;

    @ApiModelProperty("资源类型")
    private ResourceType type;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
```

## 异常处理模式

### 业务异常
```java
// 抛出业务异常
throw new BusinessException("业务逻辑错误");
throw new BusinessException(ErrorCode.RESOURCE_NOT_FOUND, "资源不存在");

// 参数校验异常会被GlobalExceptionHandler自动处理
```

## 日志记录模式

### 标准日志格式
```java
// 方法入口日志
log.info("开始处理请求，方法: {}，参数: {}", "methodName", request);

// 业务关键节点日志
log.info("执行关键业务逻辑，参数: {}", params);

// 异常日志
log.error("处理请求失败，方法: {}，异常信息: {}", "methodName", e.getMessage(), e);

// 方法出口日志
log.info("请求处理完成，方法: {}，结果: {}", "methodName", response);
```

## 缓存使用模式

### 缓存注解使用
```java
@Cacheable(value = "resourceCache", key = "#id")
public ResourceResponse getById(Long id) {
    // 方法实现
}

@CacheEvict(value = "resourceCache", key = "#id")
public void delete(Long id) {
    // 删除逻辑
}

@CacheEvict(value = "resourceCache", allEntries = true)
public void clearAllCache() {
    // 清空所有缓存
}
```
