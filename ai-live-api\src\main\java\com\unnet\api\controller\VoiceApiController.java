package com.unnet.api.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.unnet.api.config.RouterConfig;
import com.unnet.api.service.VoiceApiService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.swing.text.StyledEditorKit;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/api/v1/tts")
@Tag(name = "音频管理", description = "封装音频部分的接口")
public class VoiceApiController {

    @Autowired
    private VoiceApiService voiceApiService;

    @Resource
    private RouterConfig routerConfig;

    //音频生成接口
    @PostMapping("/call/generate_audio")
    public ResponseEntity<JSONObject> generateAudio(@RequestBody Map<String, List<Object>> request) throws Exception {
        log.info("接收到音频生成请求");
        List<Object> data = request.get("data");

        JSONObject eventId = voiceApiService.generateAudio(data);

        return ResponseEntity.ok(eventId);
    }

    @GetMapping("/call/generate_audio/{event_id}")
    public void streamAudio(@PathVariable(name="event_id") String eventId, HttpServletResponse response) throws Exception {
        // 1. 设置SSE响应头
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("X-Accel-Buffering", "no");  // 禁用Nginx等代理缓冲
        response.setBufferSize(0);  // 禁用Servlet层缓冲层

        // 2. 流式转发
        PrintWriter writer = response.getWriter();
        int i = 0;      //实际读取坐标
        int temp = 0;   //实际循环次数（1秒循环一次）
        while (true) {
            temp++;
            String resStr = routerConfig.getRedisInfo(eventId+"_res");
            if(resStr!=null && !"".equals(resStr)) {
                List<String> resList = JSONObject.parseArray(resStr, String.class);
                if (resList != null && resList.size() > 0) {
                    if (resList.size() >= i + 1) {
                        String line = resList.get(i++);
                        // 实时转发原始事件数据
                        writer.write(line + "\n");
                        writer.flush();
                        if (line.startsWith("event: complete") || line.startsWith("event: error")) {
                            break;
                        }
                    }
                }
            }
            //如果连续30秒都没有任何数据，则终止本次请求
            if(temp>=30 && i==0){
                break;
            }
            Thread.sleep(1000);
        }
    }

    //m3u8文件下载接口
    @GetMapping("/stream/{uuid}/{timestamp}/{seq}/playlist.m3u8")
    public ResponseEntity<byte[]> getM3U8(@PathVariable(name="uuid")String uuid,
                                          @PathVariable(name="timestamp")String timestamp,
                                          @PathVariable(name="seq")String seq,
                                          @RequestParam(name="event_id")String eventId) throws Exception {
        log.info("接收到m3u8请求");
        String path = uuid + "/" + timestamp + "/" + seq;
        return voiceApiService.getM3U8(uuid, path);
    }

    //片段音频下载接口
    @GetMapping("/stream/{uuid}/{timestamp}/{seq}/{uuid_aac}")
    public ResponseEntity<byte[]> getPartAudio(@PathVariable(name="uuid")String uuid,
                                               @PathVariable(name="timestamp")String timestamp,
                                               @PathVariable(name="seq")String seq,
                                               @PathVariable(name="uuid_aac")String uuid_aac,
                                               @RequestParam(name="event_id")String eventId) throws Exception {
        log.info("接收到片段音频请求");
        String path = uuid + "/" + timestamp + "/" + seq;
        return voiceApiService.getPartAudio(uuid, path, uuid_aac);
    }

    //整合音频下载接口
    @GetMapping("/stream/{uuid}/{timestamp}/{seq}/playlist-file")
    public ResponseEntity<byte[]> getFullAudio(@PathVariable(name="uuid")String uuid,
                                                @PathVariable(name="timestamp")String timestamp,
                                                @PathVariable(name="seq")String seq,
                                                @RequestParam(name="event_id")String eventId) throws Exception {
        log.info("接收到整合音频请求");
        // 业务逻辑处理
        String path = uuid + "/" + timestamp + "/" + seq;
        return voiceApiService.getFullAudio(uuid, path);
    }
}
