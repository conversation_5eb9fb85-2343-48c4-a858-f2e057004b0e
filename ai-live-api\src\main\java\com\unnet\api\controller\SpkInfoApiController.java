package com.unnet.api.controller;

import com.unnet.api.config.RouterConfig;
import com.unnet.api.entity.ApiResp;
import com.unnet.api.entity.Voice;
import com.unnet.api.service.SpkInfoApiService;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Delete;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/api/v1/tts")
@Tag(name = "音色管理", description = "封装音色部分的接口")
public class SpkInfoApiController {

    @Autowired
    private SpkInfoApiService spkInfoApiService;

    //音色查询接口
    @GetMapping("/api/get_available_spks")
    public ResponseEntity<List<Map<String,String>>> ttsFind(@RequestParam(name="user_id")String userId){
        return ResponseEntity.ok(spkInfoApiService.find(userId));
    }


    public boolean isWavFile(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            byte[] header = new byte[4];
            if (inputStream.read(header) != 4) {
                return false; // 文件太小，无法读取4字节
            }
            return new String(header).equals("RIFF"); // 检查是否是RIFF
        }
    }

    //音色上传接口
    @PostMapping(value = "/api/upload_spk_info", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResp<Map<String,String>>> ttsUpload(@RequestParam(name="prompt_text")String promptText,
                                                                 @RequestParam(name="spk_name")String spkName,
                                                                 @RequestParam(name="spk_gender")String spkGender,
                                                                 @RequestParam(name="spk_desc")String spkDesc,
                                                                 @RequestParam(name="user_id")String userId,
                                                                 @RequestPart("audio_file") @Schema(type = "string", format = "binary") MultipartFile audioFile ) throws IOException {
        // 获取文件扩展名
        String fileName = audioFile.getOriginalFilename();
        String fileExtension = fileName != null ? fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase() : "";
        // 如果是 WAV 文件，校验 RIFF 头
        if ("wav".equals(fileExtension) && !isWavFile(audioFile)) {
            return ResponseEntity.badRequest().body(ApiResp.error(500,"WAV文件必须是有效的格式（以RIFF开头）"));
        }
        Voice voice = Voice.builder().audioFileUrl(audioFile).promptText(promptText).spkName(spkName).spkGender(spkGender).spkDesc(spkDesc).userId(userId).build();
        return ResponseEntity.ok(ApiResp.success(spkInfoApiService.upload(voice)));
    }

    //音色删除接口
    @DeleteMapping("/api/delete_spk_info")
    public ResponseEntity<ApiResp<String>> ttsDelete(@RequestParam(name="spk_name") String spkName,@RequestParam(name="user_id") String userId){
        spkInfoApiService.delete(spkName,userId);
        return ResponseEntity.ok(ApiResp.success(null));
    }

    //试听音频接口下载
    @GetMapping("/api/download_test_audio")
    public ResponseEntity<byte[]> ttsDownload(@RequestParam(name="spk_name") String spkName,@RequestParam(name="user_id") String userId) throws Exception {
        return spkInfoApiService.download(spkName,userId, 2);
    }

    //提示音频下载
    @GetMapping("/api/download_tip_audio")
    public ResponseEntity<byte[]> ttsDownloadTest(@RequestParam(name="spk_name") String spkName,@RequestParam(name="user_id") String userId) throws Exception {
        return spkInfoApiService.download(spkName,userId,1);
    }

    //提示文本下载
    @GetMapping("/api/download_tip_word")
    public ResponseEntity<String> ttsDownloadTipword(@RequestParam(name="spk_name") String spkName,@RequestParam(name="user_id") String userId) throws Exception {
        return ResponseEntity.ok(spkInfoApiService.downloadTips(spkName,userId));
    }

    @Resource
    private RouterConfig routerConfig;

    //node节点信息获取
    @GetMapping("/api/get_node_info")
    public ResponseEntity<String> getNodeInfo() {
        return ResponseEntity.ok(routerConfig.getIpInfo());
    }

    //node节点信息获取
    @GetMapping("/api/get_redis_info")
    public ResponseEntity<String> getRedisInfo(@RequestParam(name="key") String key) {
        return ResponseEntity.ok(routerConfig.getRedisInfo(key));
    }

    //node节点信息更新
    @PostMapping("/api/set_node_info")
    public ResponseEntity<String> setNodeInfo(@RequestParam(name="servers") String servers) {
        routerConfig.setIpInfo(servers);
        return ResponseEntity.ok(null);
    }
}
