package com.unnet.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 克隆文档请求
 */
@Data
@Schema(description = "克隆文档请求")
public class CloneDocumentRequest {
    
    @NotBlank(message = "源知识库ID不能为空")
    @Schema(description = "源知识库ID", required = true)
    private String source_dataset_id;
    
    @NotBlank(message = "目标知识库ID不能为空")
    @Schema(description = "目标知识库ID", required = true)
    private String target_dataset_id;
} 