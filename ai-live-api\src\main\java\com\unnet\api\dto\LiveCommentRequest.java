package com.unnet.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "直播间弹幕问题请求")
public class LiveCommentRequest {

    @NotBlank(message = "用户问题不能为空")
    @Schema(description = "用户的弹幕问题", required = true, example = "这款车的安全性能怎么样？")
    private String question;

    @NotBlank(message = "知识库ID不能为空")
    @Schema(description = "对应的知识库ID", required = true, example = "dataset_123")
    private String datasetId;

    @NotNull(message = "直播点位列表不能为null")
    @Schema(description = "直播点位列表", required = true)
    private List<String> livePoints;

    @Schema(description = "会话ID (可选, 如果不提供或为空，将创建新会话)")
    private String sessionId;

    @Schema(description = "额外信息，可补充直播上下文到系统提示词")
    private String extraInfo;
} 