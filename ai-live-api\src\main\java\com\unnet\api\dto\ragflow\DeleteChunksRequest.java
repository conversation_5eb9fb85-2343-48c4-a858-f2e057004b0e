package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "删除分段的请求体")
public class DeleteChunksRequest {

    @Schema(description = "要删除的分段ID列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"chunk_id_1\", \"chunk_id_2\"]")
    private List<String> chunk_ids;
} 