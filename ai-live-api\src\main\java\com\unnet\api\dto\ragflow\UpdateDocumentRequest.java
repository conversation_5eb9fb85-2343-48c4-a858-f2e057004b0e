package com.unnet.api.dto.ragflow;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新文档请求对象 (简化版)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateDocumentRequest {
    
    /**
     * 文档名称
     */
    private String name;
    
    /**
     * 文档状态 (1-启用, 0-未启用)
     */
    private String status;
    
    /**
     * 分块方法 (如 "qa" 用于QA对处理)
     */
    private String chunk_method;
} 