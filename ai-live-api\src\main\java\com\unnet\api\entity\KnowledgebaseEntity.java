package com.unnet.api.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;

/**
 * 知识库实体类
 */
@Entity
@Table(name = "knowledgebase")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgebaseEntity {
    
    /**
     * 知识库ID (对应RagFlow的dataset_id)
     */
    @Id
    @Column(name = "id", length = 64, nullable = false)
    private String id;
    
    /**
     * 知识库名称
     */
    @Column(name = "name", length = 255)
    private String name;
    
    /**
     * 文档数量
     */
    @Column(name = "doc_num", nullable = false)
    private Integer docNum = 0;
    
    /**
     * token数量
     */
    @Column(name = "token_num", nullable = false)
    private Long tokenNum = 0L;
    
    /**
     * 分段数量
     */
    @Column(name = "chunk_num", nullable = false)
    private Integer chunkNum = 0;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 255)
    private String createdBy;
    
    /**
     * 描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
} 