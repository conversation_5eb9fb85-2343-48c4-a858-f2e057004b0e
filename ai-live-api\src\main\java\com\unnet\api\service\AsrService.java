package com.unnet.api.service;

import com.unnet.api.dto.AsrTaskCreateResponse;
import com.unnet.api.dto.AsrTaskStatusResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * ASR语音识别服务接口
 */
public interface AsrService {
    
    /**
     * 创建ASR任务
     * 
     * @param file 音频文件
     * @return 任务创建响应
     */
    AsrTaskCreateResponse createAsrTask(MultipartFile file);
    
    /**
     * 查询ASR任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态响应
     */
    AsrTaskStatusResponse getTaskStatus(String taskId);
} 