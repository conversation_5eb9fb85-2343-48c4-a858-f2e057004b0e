package com.unnet.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ASR任务状态查询响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ASR任务状态查询响应")
public class AsrTaskStatusResponse {
    
    @Schema(description = "任务状态")
    private String status;
    
    @Schema(description = "识别文本内容")
    private String text;
    
    @Schema(description = "任务ID")
    @JsonProperty("task_id")
    private String taskId;
    
    @Schema(description = "创建时间")
    @JsonProperty("created_at")
    private String createdAt;
    
    @Schema(description = "开始时间")
    @JsonProperty("started_at")
    private String startedAt;
    
    @Schema(description = "完成时间")
    @JsonProperty("completed_at")
    private String completedAt;
    
    @Schema(description = "处理时间(秒)")
    @JsonProperty("processing_time")
    private Double processingTime;
} 