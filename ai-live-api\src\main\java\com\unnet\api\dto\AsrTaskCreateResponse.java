package com.unnet.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ASR任务创建响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "ASR任务创建响应")
public class AsrTaskCreateResponse {
    
    @Schema(description = "任务状态")
    private String status;
    
    @Schema(description = "任务ID")
    @JsonProperty("task_id")
    private String taskId;
} 