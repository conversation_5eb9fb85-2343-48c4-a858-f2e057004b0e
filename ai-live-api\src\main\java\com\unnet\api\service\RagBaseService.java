package com.unnet.api.service;

import com.unnet.api.dto.ApiResp;
import com.unnet.api.dto.ragflow.DatasetRequest;
import com.unnet.api.dto.ragflow.RagFlowResponse;
import com.unnet.api.dto.ragflow.DocumentDownloadResult;
import com.unnet.api.dto.ragflow.UpdateDocumentRequest;
import com.unnet.api.dto.ragflow.AddChunkToDocumentRequest;
import com.unnet.api.dto.ragflow.DeleteChunksRequest;
import com.unnet.api.dto.ragflow.UpdateChunkRequest;
import com.unnet.api.dto.ragflow.ChatAssistantRequest;
import com.unnet.api.dto.ragflow.QAUploadRequest;
import com.unnet.api.dto.ragflow.ParseDocumentsRequest;
import com.unnet.api.dto.ragflow.QAQueryListResponse;
import com.unnet.api.dto.ragflow.QAPairResponse;
import com.unnet.api.dto.ragflow.QAPairListResponse;
import com.unnet.api.dto.ragflow.QAUploadResponse;
import com.unnet.api.dto.ragflow.QADeleteRequest;

import java.util.List;
import java.util.Map;

/**
 * RagFlow API服务接口
 */
public interface RagBaseService {
    
    /**
     * 创建知识库
     *
     * @param request 知识库请求
     * @return 创建结果
     */
    RagFlowResponse<?> createDataset(DatasetRequest request);
    
    /**
     * 删除知识库
     *
     * @param ids 知识库ID列表
     * @return 删除结果
     */
    RagFlowResponse<?> deleteDatasets(List<String> ids);
    
    /**
     * 更新知识库
     *
     * @param id 知识库ID
     * @param request 更新请求
     * @return 更新结果
     */
    RagFlowResponse<?> updateDataset(String id, DatasetRequest request);
    
    /**
     * 获取知识库列表
     *
     * @param params 查询参数
     * @return 知识库列表
     */
    RagFlowResponse<?> listDatasets(Map<String, Object> params);
    
    /**
     * 上传文档到知识库
     *
     * @param datasetId 知识库ID
     * @param file 文件数据
     * @param fileName 文件名
     * @return 上传结果
     */
    RagFlowResponse<?> uploadDocument(String datasetId, byte[] file, String fileName);
    
    /**
     * 批量上传文档到知识库
     *
     * @param datasetId 知识库ID
     * @param files 文件数据列表
     * @param fileNames 文件名列表
     * @return 上传结果
     */
    RagFlowResponse<?> uploadDocuments(String datasetId, List<byte[]> files, List<String> fileNames);
    
    /**
     * 获取知识库中的文档列表
     *
     * @param datasetId 知识库ID
     * @param params 查询参数
     * @return 文档列表
     */
    RagFlowResponse<?> listDocuments(String datasetId, Map<String, Object> params);


    /*
       过滤qa类型文件的文档列表
    */
    RagFlowResponse<?> listDocumentsFilter(String datasetId, Map<String, Object> params);
    /**
     * 删除知识库中的文档
     *
     * @param datasetId 知识库ID
     * @param ids 文档ID列表
     * @return 删除结果
     */
    RagFlowResponse<?> deleteDocuments(String datasetId, List<String> ids);
    
    /**
     * 下载知识库中的文档
     *
     * @param datasetId 知识库ID
     * @param documentId 文档ID
     * @return 文档下载结果（包含文件内容和文件名）
     */
    DocumentDownloadResult downloadDocument(String datasetId, String documentId);
    
    /**
     * 更新知识库中的文档配置
     *
     * @param datasetId 知识库ID
     * @param documentId 文档ID
     * @param request 更新请求
     * @return 更新结果
     */
    ApiResp<?> updateDocument(String datasetId, String documentId, UpdateDocumentRequest request);

    /**
     * 克隆知识库中的文档配置
     *
     * @param sourceId 知识库ID
     * @param targetId 目标知识库ID
     * @return 更新结果
     */

    ApiResp<?> cloneDocument(String sourceId,String targetId);
    // ---- Chunk Management APIs ----

    /**
     * 向指定文档添加新的分段
     * @param datasetId 知识库ID
     * @param documentId 文档ID
     * @param request 包含分段内容的请求体
     * @return RagFlow API 响应
     */
    RagFlowResponse<?> addChunkToDocument(String datasetId, String documentId, AddChunkToDocumentRequest request);

    /**
     * 列出指定文档的分段
     * @param datasetId 知识库ID
     * @param documentId 文档ID
     * @param params 查询参数 (e.g., keywords, page, page_size, id)
     * @return RagFlow API 响应
     */
    RagFlowResponse<?> listChunks(String datasetId, String documentId, Map<String, Object> params);

    /**
     * 删除指定的分段
     * @param datasetId 知识库ID
     * @param documentId 文档ID
     * @param request 包含 chunk_ids 列表的请求体
     * @return RagFlow API 响应
     */
    RagFlowResponse<?> deleteChunks(String datasetId, String documentId, DeleteChunksRequest request);

    /**
     * 更新指定知识库、文档中特定分段的信息。
     * @param datasetId 知识库ID
     * @param documentId 文档ID
     * @param chunkId 要更新的分段ID
     * @param request 包含分段更新信息的请求体 (可选字段: content, important_keywords, available)
     * @return RagFlow API 响应
     */
    RagFlowResponse<?> updateChunkInDocument(String datasetId, String documentId, String chunkId, UpdateChunkRequest request);

    /**
     * 创建聊天助手
     *
     * @param request 聊天助手创建请求
     * @return 创建结果
     */
    RagFlowResponse<?> createChatAssistant(ChatAssistantRequest request);

    /**
     * 删除聊天助手
     *
     * @param ids 聊天助手ID列表
     * @return 删除结果
     */
    RagFlowResponse<?> deleteChatAssistants(List<String> ids);

    /**
     * 上传QA对到知识库
     *
     * @param datasetId 知识库ID
     * @param qaList QA对列表
     * @return 上传成功的QA对信息列表
     */
    List<QAUploadResponse> uploadQAToDataset(String datasetId, List<QAUploadRequest> qaList);

    /**
     * 删除知识库中的QA对
     *
     * @param datasetId 知识库ID
     * @param qaIds QA对ID列表
     * @return 删除结果
     */
    RagFlowResponse<?> deleteQAPairs(String datasetId, List<String> qaIds);

    /**
     * 更新知识库中的QA对
     *
     * @param datasetId 知识库ID
     * @param qaId QA对ID
     * @param question 新的问题
     * @param answer 新的答案
     * @return 更新结果
     */
    RagFlowResponse<?> updateQAPair(String datasetId, String qaId, String question, String answer);

    /**
     * 解析知识库中的文档
     *
     * @param datasetId 知识库ID
     * @param request 解析请求
     * @return 解析结果
     */
    RagFlowResponse<?> parseDocuments(String datasetId, ParseDocumentsRequest request);

    /**
     * 查询多个知识库中的QA对
     *
     * @param datasetIds 知识库ID列表
     * @return QA对查询结果
     */
    QAQueryListResponse queryQAPairs(List<String> datasetIds);

    /**
     * 查询多个知识库中的QA对（简化格式）
     *
     * @param datasetIds 知识库ID列表
     * @return QA对列表（拆分question和answer，包含总数）
     */
    QAPairListResponse queryQAPairsSimplified(List<String> datasetIds);

    /**
     * 查询多个知识库中的QA对（支持关键词搜索）
     *
     * @param datasetIds 知识库ID列表
     * @param keywords 搜索关键词（可选）
     * @param page 页码（可选，默认1）
     * @param pageSize 每页条数（可选，默认50）
     * @return QA对列表（拆分question和answer，包含总数和分页信息）
     */
    QAPairListResponse queryQAPairsWithSearch(List<String> datasetIds, String keywords, Integer page, Integer pageSize);

    /**
     * 直接更新文档的parser_id和parser_config字段
     *
     * @param documentId 文档ID
     * @param parserId 解析器ID
     * @param parserConfig 解析器配置
     */
    void updateDocumentParserFields(String documentId, String parserId, String parserConfig);

    /**
     * 复制对话配置
     * 从源对话记录复制配置到指定的目标对话记录
     * 包含的字段：prompt_config、similarity_threshold、vector_similarity_weight、top_n、top_k、rerank_id
     *
     * @param sourceDialogId 源对话ID
     * @param targetDialogIds 目标对话ID列表
     * @return 复制结果，包含受影响的记录数
     */
    ApiResp<?> copyDialogConfig(String sourceDialogId, List<String> targetDialogIds);

} 