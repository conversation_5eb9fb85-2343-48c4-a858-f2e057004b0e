package com.unnet.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "直播间弹幕问题响应")
public class LiveCommentResponse {

    @Schema(description = "生成的直播脚本内容", example = "咱们这款车的车身可是下了大功夫的！整体采用笼式高刚性结构...")
    private String script;

    @Schema(description = "相关的直播点位", example = "车身")
    private String location;

    @Schema(description = "是否匹配到QA")
    private Boolean isMatchQa;

    @Schema(description = "QA对的ID值")
    private String qaId;

    @Schema(description = "会话ID")
    private String sessionId;

    public LiveCommentResponse(String script, String location) {
        this.script = script;
        this.location = location;
    }
}