package com.unnet.api.dto.ragflow;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 解析文档请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "解析文档请求")
public class ParseDocumentsRequest {
    
    @Schema(description = "要解析的文档ID列表", required = true, example = "[\"97a5f1c2759811efaa500242ac120004\",\"97ad64b6759811ef9fc30242ac120004\"]")
    private List<String> document_ids;
} 