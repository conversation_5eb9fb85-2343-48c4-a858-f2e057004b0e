# 4S店AI直播平台大模型相关API服务

## 技术栈

* Spring Boot 2.7.x
* Spring Web (MVC)
* Bean Validation
* Spring AOP
* Spring Cache + Caffeine
* Lombok
* SLF4J (Logback)

## 快速开始

### 前提条件

* JDK 8+
* Maven 3.6+ 或 Gradle 7.0+

### 安装与运行

1. 克隆仓库:
```bash
git clone https://git.un-net.com/examples/ai-live-api.git
cd ai-live-api
```

2. 构建项目:
```bash
./mvn clean package -DskipTests
```

3. 运行应用:
```bash
./mvn spring-boot:run
```

4. 访问: http://localhost:8081/api/swagger-ui/index.html

### API 认证与防重放机制

#### API Key 认证

API Key 通过请求头 `X-API-KEY` 传递。在 `application.yml` 中配置的有效API Key为：
- `api-key-client1`
- `api-key-client2`

#### Nonce 和时间戳

为防止请求被重放，每个API请求都需要包含以下两个请求头：

1. **X-Request-Nonce**: 
   - 一个请求唯一的随机字符串
   - 服务器会缓存已处理的Nonce，重复的Nonce将被拒绝
   - 推荐格式：UUID（例如：`123e4567-e89b-12d3-a456-************`）
   - 每个请求必须使用不同的Nonce

2. **X-Request-Timestamp**:
   - Unix时间戳（从1970年1月1日至今的秒数）
   - 必须在服务器当前时间的±5分钟范围内
   - 例如：`1633933200`

##### 客户端生成方法

**Java**:
```java
// 生成Nonce
String nonce = UUID.randomUUID().toString();

// 生成时间戳
long timestamp = System.currentTimeMillis() / 1000L;
```

**JavaScript/Node.js**:
```javascript
// 生成Nonce
const { v4: uuidv4 } = require('uuid');
const nonce = uuidv4();
// 或浏览器环境
// const nonce = crypto.randomUUID();

// 生成时间戳
const timestamp = Math.floor(Date.now() / 1000);
```

**Python**:
```python
# 生成Nonce
import uuid
nonce = str(uuid.uuid4())

# 生成时间戳
import time
timestamp = int(time.time())
```

### API 调用示例

**使用 API Key 进行认证:**

```bash
curl -X GET \
  http://localhost:8080/api/demo/hello \
  -H 'X-API-KEY: api-key-client1' \
  -H 'X-Request-Nonce: 123e4567-e89b-12d3-a456-************' \
  -H 'X-Request-Timestamp: 1633933200'
```

**发送 POST 请求并进行参数校验:**

```bash
curl -X POST \
  http://localhost:8080/api/demo/users \
  -H 'X-API-KEY: api-key-client1' \
  -H 'X-Request-Nonce: 123e4567-e89b-12d3-a456-************' \
  -H 'X-Request-Timestamp: 1633933200' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "age": 25,
    "phone": "13800138000"
}'
```

## 项目结构

```
src/main/java/com/example/api/
├── JmanulApiApplication.java          # 主应用类
├── aspect
│   └── LoggingAspect.java               # 日志切面
├── config
│   ├── AppProperties.java               # 应用配置属性类
│   └── CacheConfig.java                 # 缓存配置
├── controller
│   └── DemoController.java              # 示例控制器
├── dto
│   ├── ApiResponse.java                 # 通用API响应DTO
│   └── UserRequest.java                 # 示例请求DTO
├── exception
│   ├── BusinessException.java           # 业务异常类
│   ├── ErrorResponse.java               # 错误响应类
│   └── GlobalExceptionHandler.java      # 全局异常处理器
└── filter
    ├── ApiKeyAuthFilter.java            # API Key认证过滤器
    └── ReplayAttackFilter.java          # 防重放攻击过滤器
```

## 配置

主要配置在 `application-dev.yml` 及 `application-prod.yml` 文件中:

```yaml
app:
  # API密钥配置
  api:
    keys:
      - key: "api-key-client1"
        clientId: "client1"
      - key: "api-key-client2"
        clientId: "client2"
  # 防重放攻击配置
  replay-attack:
    timestamp-tolerance-seconds: 300  # 5分钟
    nonce-cache-minutes: 10  # Nonce缓存时间（分钟）
```
