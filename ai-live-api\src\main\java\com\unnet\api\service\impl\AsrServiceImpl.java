package com.unnet.api.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.api.dto.AsrTaskCreateResponse;
import com.unnet.api.dto.AsrTaskStatusResponse;
import com.unnet.api.enums.AsrTaskStatus;
import com.unnet.api.service.AsrService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * ASR语音识别服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsrServiceImpl implements AsrService {
    
    @Value("${asr.service.url:http://localhost:5077}")
    private String asrServiceUrl;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public AsrTaskCreateResponse createAsrTask(MultipartFile file) {
        try {
            log.info("开始创建ASR任务，文件名: {}, 文件大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());
            
            // 构建请求URL
            String url = asrServiceUrl + "/recognize/async";
            
            // 构建multipart/form-data请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            body.add("file", fileResource);
            
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
            // 发送请求并获取原始响应
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.POST, requestEntity, String.class);
            
            // 解析原始响应
            JsonNode jsonResponse = objectMapper.readTree(response.getBody());
            
            // 检查请求是否成功
            if (jsonResponse.has("code") && jsonResponse.get("code").asInt() == 0) {
                // 提取核心信息
                AsrTaskCreateResponse result = new AsrTaskCreateResponse();
                result.setStatus(jsonResponse.get("status").asText());
                result.setTaskId(jsonResponse.get("task_id").asText());
                
                log.info("ASR任务创建成功，任务ID: {}, 状态: {}", result.getTaskId(), result.getStatus());
                return result;
            } else {
                log.warn("ASR任务创建失败: {}", jsonResponse.get("message").asText());
                throw new RuntimeException("ASR任务创建失败: " + jsonResponse.get("message").asText());
            }
            
        } catch (Exception e) {
            log.error("创建ASR任务失败", e);
            // 返回失败响应
            AsrTaskCreateResponse errorResponse = new AsrTaskCreateResponse();
            errorResponse.setStatus(AsrTaskStatus.FAILED.getValue());
            return errorResponse;
        }
    }
    
    @Override
    public AsrTaskStatusResponse getTaskStatus(String taskId) {
        try {
            log.info("查询ASR任务状态，任务ID: {}", taskId);
            
            // 构建请求URL
            String url = asrServiceUrl + "/recognize/status/" + taskId;
            
            // 发送GET请求
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            
            // 解析原始响应
            JsonNode jsonResponse = objectMapper.readTree(response.getBody());
            
            // 检查请求是否成功
            if (jsonResponse.has("code") && jsonResponse.get("code").asInt() == 0) {
                // 提取核心信息
                JsonNode data = jsonResponse.get("data");
                AsrTaskStatusResponse result = new AsrTaskStatusResponse();
                
                result.setTaskId(data.get("task_id").asText());
                result.setStatus(data.get("status").asText());
                result.setCreatedAt(data.get("created_at").asText());
                result.setStartedAt(data.get("started_at").asText());
                result.setCompletedAt(data.get("completed_at").asText());
                result.setProcessingTime(data.get("processing_time").asDouble());
                
                // 提取识别结果文本
                if (data.has("result") && data.get("result").has("text")) {
                    result.setText(data.get("result").get("text").asText());
                }
                
                log.info("ASR任务状态查询成功，任务ID: {}, 状态: {}", taskId, result.getStatus());
                return result;
            } else {
                log.warn("ASR任务状态查询失败，任务ID: {}", taskId);
                throw new RuntimeException("任务不存在或查询失败");
            }
            
        } catch (Exception e) {
            log.error("查询ASR任务状态失败，任务ID: {}", taskId, e);
            // 返回失败响应
            AsrTaskStatusResponse errorResponse = new AsrTaskStatusResponse();
            errorResponse.setTaskId(taskId);
            errorResponse.setStatus(AsrTaskStatus.FAILED.getValue());
            return errorResponse;
        }
    }
} 