package com.unnet.api;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@EnableCaching
@MapperScan("com.unnet.api.mapper")
@EnableJpaRepositories("com.unnet.api.repository")
public class AiLiveApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiLiveApiApplication.class, args);
    }
} 