spring:
  datasource:
    url: *************************************************************************************************************
    username: root
    password: infini_rag_flow
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 100      # 最大连接数，根据实际并发调整
      minimum-idle: 20           # 最小空闲连接数
      idle-timeout: 300000       # 空闲连接存活时间（毫秒），5分钟，适当延长空闲时间
      connection-timeout: 30000  # 获取连接超时时间（毫秒）
      max-lifetime: 1200000      # 连接最大存活时间（毫秒）
  jpa:
    hibernate:
      # 开发环境使用update，会自动更新表结构（保留现有数据）
      # 如果不希望自动建表，可以改为：validate（仅验证）或 none（不操作）
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

  elasticsearch:
    uris: http://**************:1200
    username: elastic
    password: infini_rag_flow

redis:
  # 地址
  host: *************
  port: 30379
  database: 0
  # 密码
  password: password4redis

# 应用自定义配置
app:
  # API密钥配置
  api:
    keys:
      - key: "api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e"
        clientId: "haiyun-ai-live"
      - key: "api-key-client2"
        clientId: "client2"
  security:
    # 防重放攻击配置
    replay-protection:
      enabled: false  # 是否启用防重放攻击保护
      timestamp-tolerance-seconds: 300  # 时间戳容忍度（秒）
      nonce-cache-minutes: 10  # Nonce缓存时间（分钟）
logging:
  level:
    com.unnet.api.service.impl.ApiLogServiceImpl: OFF
# 开发环境配置
server:
  port: 8081

# 开发环境启用Swagger
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

# RagFlow API配置
ragflow:
  api:
    baseUrl: http://**************:18080  # 生产环境RagFlow API基础URL，请根据实际情况修改
    key: ragflow-hjMjZkZDBlMzdiMDExZjBiZjg0MDI0Mm  # 生产环境RagFlow API密钥，请替换为实际的API Key

# LLM配置（自定义配置）
llm:
  base-url: http://************:9997/v1
  model: qwen3
  # 调整超时时间到120秒，平衡性能和稳定性
  # 过长的超时可能导致用户体验差，过短可能导致复杂请求失败
  timeout: 120

# ASR语音识别服务配置
asr:
  service:
    url: http://localhost:5077