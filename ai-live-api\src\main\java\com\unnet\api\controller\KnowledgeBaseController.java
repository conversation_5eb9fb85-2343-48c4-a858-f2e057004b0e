package com.unnet.api.controller;

import com.unnet.api.dto.ApiResp;
import com.unnet.api.dto.CloneDocumentRequest;
import com.unnet.api.dto.ragflow.*;
import com.unnet.api.service.RagBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RagFlow API控制器
 */
@RestController
@RequestMapping("/api/v1")
@Slf4j
@Tag(name = "知识库相关 API", description = "知识库、文档及分段管理API")
public class KnowledgeBaseController {
    
    private final RagBaseService ragBaseService;
    
    public KnowledgeBaseController(RagBaseService ragBaseService) {
        this.ragBaseService = ragBaseService;
    }
    
    /**
     * 创建知识库
     */
    @PostMapping("/datasets")
    @Operation(summary = "创建知识库", description = "创建一个新的RagFlow知识库")
    public ApiResp<?> createDataset(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "知识库创建请求", 
                required = true, 
                content = @Content(schema = @Schema(implementation = DatasetRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody DatasetRequest request) {
        
        log.info("创建知识库: {}", request);
        RagFlowResponse<?> response = ragBaseService.createDataset(request);
        
        return ApiResp.success("知识库创建成功", response.getData());
    }
    
    /**
     * 删除知识库
     */
    @DeleteMapping("/datasets")
    @Operation(summary = "删除知识库", description = "删除指定ID的知识库")
    public ApiResp<?> deleteDatasets(
             @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "知识库删除请求，包含知识库ID列表", 
                required = true, 
                content = @Content(schema = @Schema(implementation = DeleteRequest.class)))
             @org.springframework.web.bind.annotation.RequestBody DeleteRequest request) {
        
        // 添加参数验证，防止意外删除所有知识库
        if (request == null) {
            log.error("删除知识库失败：请求体为空");
            return ApiResp.error(400, "请求体不能为空");
        }
        
        if (request.getIds() == null || request.getIds().isEmpty()) {
            log.error("删除知识库失败：知识库ID列表为空");
            return ApiResp.error(400, "知识库ID列表不能为空，请指定要删除的知识库ID");
        }
        
        log.info("删除知识库: {}", request);
        RagFlowResponse<?> response = ragBaseService.deleteDatasets(request.getIds());
        
        return ApiResp.success("知识库删除成功", response.getData());
    }
    
    /**
     * 更新知识库
     */
    @PutMapping("/datasets/{id}")
    @Operation(summary = "更新知识库", description = "更新指定ID的知识库信息")
    public ApiResp<?> updateDataset(
            @Parameter(name = "id", description = "知识库ID", required = true, example = "dataset_id_123")
            @PathVariable String id,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "知识库更新请求", 
                required = true, 
                content = @Content(schema = @Schema(implementation = DatasetRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody DatasetRequest request) {
        
        log.info("更新知识库，ID: {}, 请求: {}", id, request);
        RagFlowResponse<?> response = ragBaseService.updateDataset(id, request);
        
        return ApiResp.success("知识库更新成功", response.getData());
    }
    
    /**
     * 获取知识库列表
     */
    @GetMapping("/datasets")
    @Operation(summary = "获取知识库列表", description = "获取所有知识库或根据条件筛选")
    public ApiResp<?> listDatasets(
            @Parameter(name = "page", description = "页码", example = "1") 
            @RequestParam(required = false) Integer page,
            @Parameter(name = "page_size", description = "每页条数", example = "10")
            @RequestParam(required = false, name = "page_size") Integer pageSize,
            @Parameter(name = "orderBy", description = "排序字段 (如 create_time, update_time)", example = "create_time") 
            @RequestParam(required = false, name = "orderby") String orderBy,
            @Parameter(name = "desc", description = "是否降序排列", example = "true") 
            @RequestParam(required = false) Boolean desc,
            @Parameter(name = "name", description = "按知识库名称进行模糊筛选", example = "我的知识库") 
            @RequestParam(required = false) String name,
            @Parameter(name = "id", description = "按知识库ID精确筛选", example = "dataset_id_123")
            @RequestParam(required = false) String id) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("page", page != null ? page : 1);
        params.put("page_size", pageSize != null ? pageSize : 30);
        params.put("orderby", orderBy != null ? orderBy : "create_time");
        params.put("desc", desc != null ? desc : true);
        
        if (name != null) {
            params.put("name", name);
        }
        if (id != null) {
            params.put("id", id);
        }
        
        log.info("获取知识库列表，参数: {}", params);
        RagFlowResponse<?> response = ragBaseService.listDatasets(params);
        
        return ApiResp.success(response.getData());
    }
    
    /**
     * 上传文档到知识库
     */
    @PostMapping(value = "/datasets/{datasetId}/documents", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传知识库文档", 
               description = "上传单个或多个文档到指定知识库。在file字段中可以选择多个文件进行批量上传")
    public ApiResp<?> uploadDocument(
            @Parameter(description = "目标知识库的ID", required = true, example = "dataset_id_abc") 
            @PathVariable String datasetId,
            @Parameter(description = "要上传的一个或多个文件。选择多个文件进行批量上传。", required = true)
            @RequestParam("file") List<MultipartFile> files) throws IOException {
        
        if (files == null || files.isEmpty()) {
            return ApiResp.error(400, "请选择要上传的文件");
        }
        
        MultipartFile[] fileArray = files.toArray(new MultipartFile[0]);
        
        if (fileArray.length == 1) {
            MultipartFile file = fileArray[0];
            log.info("上传单个文档到知识库，知识库ID: {}, 文件名: {}", datasetId, file.getOriginalFilename());
            RagFlowResponse<?> response = ragBaseService.uploadDocument(
                datasetId, 
                file.getBytes(), 
                file.getOriginalFilename()
            );
            
            // 如果上传成功且不是QA文档，自动触发解析
            if (response != null && response.getCode() != null && response.getCode() == 0) {
                triggerDocumentParsingIfNeeded(datasetId, response, file.getOriginalFilename());
            }
            
            return ApiResp.success("文档上传成功", response.getData());
        }
        
        List<byte[]> fileDataList = new ArrayList<>();
        List<String> fileNameList = new ArrayList<>();
        for (MultipartFile file : fileArray) {
            if (file.isEmpty()) {
                continue;
            }
            fileDataList.add(file.getBytes());
            fileNameList.add(file.getOriginalFilename());
        }
        
        if (fileDataList.isEmpty()) {
            return ApiResp.error(400, "没有有效的文件可以上传");
        }
        
        log.info("批量上传文档到知识库，知识库ID: {}, 文件数量: {}, 文件名: {}", 
                datasetId, fileDataList.size(), fileNameList);
        RagFlowResponse<?> response = ragBaseService.uploadDocuments(
            datasetId, 
            fileDataList, 
            fileNameList
        );
        
        // 如果批量上传成功，自动触发解析（排除QA文档）
        if (response != null && response.getCode() != null && response.getCode() == 0) {
            triggerBatchDocumentParsingIfNeeded(datasetId, response, fileNameList);
        }
        
        return ApiResp.success("文档批量上传成功", response.getData());
    }
    
    /**
     * 获取知识库文档列表
     */
    @GetMapping("/datasets/{datasetId}/documents")
    @Operation(summary = "查询知识库文档", description = "获取指定知识库中的文档列表")
    public ApiResp<?> listDocuments(
            @Parameter(name = "datasetId", description = "知识库ID", required = true)
            @PathVariable String datasetId,
            @Parameter(name = "keywords", description = "文档名称关键词查询")
            @RequestParam(required = false) String keywords,
            @Parameter(name = "page", description = "页码", example = "1") 
            @RequestParam(required = false) Integer page,
            @Parameter(name = "page_size", description = "每页条数", example = "10")
            @RequestParam(required = false, name = "page_size") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();

        page = page != null ? page : 1;
        pageSize = pageSize != null ? pageSize : 30;

        // 只有当documentName不为null且不为空时才添加keywords参数
        if (keywords != null && !keywords.trim().isEmpty()) {
            params.put("keywords", keywords);
        }
        params.put("page", page );
        params.put("page_size", pageSize);

        log.info("获取知识库文档列表，知识库ID: {}, 参数: {}", datasetId, params);
        RagFlowResponse<?> response = ragBaseService.listDocuments(datasetId, params);

        FilteredDocumentListResponse data = (FilteredDocumentListResponse) response.getData();

        return ApiResp.success(data);
    }

    /**
     * 删除知识库文档
     */
    @DeleteMapping("/datasets/{datasetId}/documents")
    @Operation(summary = "删除知识库文档", description = "删除指定知识库中的一个或多个文档")
    public ApiResp<?> deleteDocuments(
            @Parameter(name = "datasetId", description = "知识库ID", required = true, example = "dataset_id_abc") 
            @PathVariable String datasetId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "文档删除请求，包含要删除的文档ID列表", 
                required = true,
                content = @Content(schema = @Schema(implementation = DeleteRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody DeleteRequest request) {
        
        log.info("删除知识库文档请求，知识库ID: {}", datasetId);
        log.info("请求体对象: {}", request);
        log.info("请求体中的IDs: {}", request != null ? request.getIds() : "request为null");
        
        if (request == null) {
            log.error("删除知识库文档失败：请求体为空");
            return ApiResp.error(400, "请求体不能为空");
        }
        
        if (request.getIds() == null || request.getIds().isEmpty()) {
            log.error("删除知识库文档失败：文档ID列表为空，知识库ID: {}", datasetId);
            return ApiResp.error(400, "文档ID列表不能为空，请指定要删除的文档ID");
        }
        
        log.info("删除知识库文档，知识库ID: {}, 文档IDs: {}", datasetId, request.getIds());
        RagFlowResponse<?> response = ragBaseService.deleteDocuments(datasetId, request.getIds());
        return ApiResp.success("文档删除成功", response.getData());
    }
    
    /**
     * 下载知识库文档
     */
    @GetMapping("/datasets/{datasetId}/documents/{documentId}")
    @Operation(summary = "下载知识库文档", description = "下载指定知识库中的指定文档")
    public ResponseEntity<byte[]> downloadDocument(
            @Parameter(description = "知识库ID", required = true, example = "dataset_id_abc") 
            @PathVariable String datasetId,
            @Parameter(description = "文档ID", required = true, example = "document_id_xyz") 
            @PathVariable String documentId) {
        
        log.info("下载知识库文档，知识库ID: {}, 文档ID: {}", datasetId, documentId);
        try {
            DocumentDownloadResult downloadResult = ragBaseService.downloadDocument(datasetId, documentId);
            if (downloadResult == null || downloadResult.getContent() == null || downloadResult.getContent().length == 0) {
                return ResponseEntity.notFound().build();
            }
            HttpHeaders headers = new HttpHeaders();
            try {
                MediaType contentType = MediaType.parseMediaType(downloadResult.getContentType());
                headers.setContentType(contentType);
            } catch (Exception e) {
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            }
            headers.setContentDisposition(
                ContentDisposition.attachment()
                    .filename(downloadResult.getFileName(), StandardCharsets.UTF_8)
                    .build()
            );
            headers.setContentLength(downloadResult.getFileSize());
            return ResponseEntity.ok().headers(headers).body(downloadResult.getContent());
        } catch (Exception e) {
            log.error("下载文档失败，知识库ID: {}, 文档ID: {}", datasetId, documentId, e);
            return ResponseEntity.internalServerError().build();
        }
    }


    /**
     * 克隆知识库文档
     * 注意：若源文档的分段在解析完后手动改过不会被同步！！！！！
    */
    @PostMapping("/datasets/documents/clone")
    @Operation(summary = "克隆知识库文档", description = "克隆指定知识库")
    public ApiResp<?> cloneDocument(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "克隆文档请求", 
                required = true,
                content = @Content(schema = @Schema(implementation = CloneDocumentRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody CloneDocumentRequest request
            ) {

        log.info("克隆知识库，源文档ID: {}, 目标文档ID: {}", request.getSource_dataset_id(), request.getTarget_dataset_id());

        if (request.getSource_dataset_id().equals(request.getTarget_dataset_id())) {
            return ApiResp.error(400, "源知识库ID和目标文档ID不能相同！");
        }
        ragBaseService.cloneDocument(request.getSource_dataset_id(), request.getTarget_dataset_id());

        return ApiResp.success("知识库克隆任务创建成功！");
    }

    /**
     * 更新知识库文档配置
     */
    @PutMapping("/datasets/{datasetId}/documents/{documentId}")
    @Operation(summary = "更新知识库文档配置", description = "更新指定知识库中指定文档的配置信息，如名称或状态")
    public ApiResp<?> updateDocument(
            @Parameter(description = "知识库ID", required = true, example = "dataset_id_abc") 
            @PathVariable String datasetId,
            @Parameter(description = "文档ID", required = true, example = "document_id_xyz") 
            @PathVariable String documentId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "文档更新请求体，包含要更新的文档属性", 
                required = true,
                content = @Content(schema = @Schema(implementation = UpdateDocumentRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody UpdateDocumentRequest request) {
        
        log.info("更新知识库文档配置，知识库ID: {}, 文档ID: {}, 请求: {}", datasetId, documentId, request);
        if (request == null) {
            return ApiResp.error(400, "请求体不能为空");
        }
        try {
            ApiResp<?> response = ragBaseService.updateDocument(datasetId, documentId, request);
            if (response != null && response.getCode() != null && response.getCode() == 0) {
                return ApiResp.success("知识库文档更新成功", response.getData());
            } else {
                String errorMessage = response != null && response.getMessage() != null ? response.getMessage() : "文档配置更新失败";
                Integer errorCode = response != null && response.getCode() != null ? response.getCode() : -1;
                log.error("更新文档配置失败: {}, 知识库ID: {}, 文档ID: {}", errorMessage, datasetId, documentId);
                return ApiResp.error(errorCode, errorMessage);
            }
        } catch (Exception e) {
            log.error("更新文档配置时发生异常，知识库ID: {}, 文档ID: {}", datasetId, documentId, e);
            return ApiResp.error(500, "更新文档配置时发生内部错误: " + e.getMessage());
        }
    }
    
    // ---- Chunk Management APIs ----
    
    @PostMapping("/datasets/{datasetId}/documents/{documentId}/chunks")
    @Operation(summary = "向文档添加分段", description = "向指定知识库的指定文档中添加一个新的分段")
    public ApiResp<?> addChunkToDocument(
            @Parameter(name = "datasetId", description = "目标知识库的ID", required = true, example = "ds_abc123")
            @PathVariable String datasetId,
            @Parameter(name = "documentId", description = "目标文档的ID", required = true, example = "doc_xyz789")
            @PathVariable String documentId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "添加分段的请求体，包含分段内容等信息", 
                required = true,
                content = @Content(schema = @Schema(implementation = AddChunkToDocumentRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody AddChunkToDocumentRequest request) {
        
        log.info("向文档添加分段，知识库ID: {}, 文档ID: {}, 请求: {}", datasetId, documentId, request);
        if (request == null || request.getContent() == null || request.getContent().trim().isEmpty()) {
            return ApiResp.error(400, "请求体或分段内容不能为空");
        }
        RagFlowResponse<?> response = ragBaseService.addChunkToDocument(datasetId, documentId, request);
        if (response != null && response.getCode() == 0) {
            return ApiResp.success("分段添加成功", response.getData());
        } else {
            return ApiResp.error(response != null ? response.getCode() : 500, 
                                 response != null ? response.getMessage() : "分段添加失败");
        }
    }
    
    @GetMapping("/datasets/{datasetId}/documents/{documentId}/chunks")
    @Operation(summary = "列出文档分段", description = "获取指定知识库中指定文档的分段列表")
    public ApiResp<?> listChunks(
            @Parameter(name = "datasetId", description = "知识库ID", required = true)
            @PathVariable String datasetId,
            @Parameter(name = "documentId", description = "文档ID", required = true)
            @PathVariable String documentId,
            @Parameter(name = "keywords", description = "用于匹配分段内容的关键词")
            @RequestParam(required = false) String keywords,
            @Parameter(name = "page", description = "页码，默认为1", example = "1")
            @RequestParam(required = false) Integer page,
            @Parameter(name = "page_size", description = "每页条数，默认为1024", example = "10")
            @RequestParam(required = false, name = "page_size") Integer page_size,
            @Parameter(name = "id", description = "要检索的特定分段ID")
            @RequestParam(required = false) String id) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("page", page != null ? page : 1);
        params.put("page_size", page_size != null ? page_size : 1024);
        
        if (keywords != null && !keywords.isEmpty()) {
            params.put("keywords", keywords);
        }
        if (id != null && !id.isEmpty()) {
            params.put("id", id);
        }
        
        log.info("列出分段，知识库ID: {}, 文档ID: {}, 参数: {}", datasetId, documentId, params);
        RagFlowResponse<?> response = ragBaseService.listChunks(datasetId, documentId, params);
        
        if (response != null && response.getCode() == 0) {
            return ApiResp.success(response.getData());
        } else {
            return ApiResp.error(response != null ? response.getCode() : 500, 
                                 response != null ? response.getMessage() : "列出分段失败");
        }
    }
    
    @DeleteMapping("/datasets/{datasetId}/documents/{documentId}/chunks")
    @Operation(summary = "删除分段", description = "删除一个或多个指定的分段。注意：此接口非标准文档接口，而是直接操作chunk表。")
    public ApiResp<?> deleteChunks(
            @Parameter(name = "datasetId", description = "知识库ID", required = true, example = "dataset_id_abc")
            @PathVariable String datasetId,
            @Parameter(name = "documentId", description = "文档ID", required = true, example = "document_id_xyz")
            @PathVariable String documentId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "删除分段请求，包含要删除的分段ID列表 (chunk_ids)", 
                required = true,
                content = @Content(schema = @Schema(implementation = DeleteChunksRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody DeleteChunksRequest request) {
        
        log.info("删除分段，知识库ID: {}, 文档ID: {}, 请求: {}", datasetId, documentId, request);
        if (request == null || request.getChunk_ids() == null || request.getChunk_ids().isEmpty()) {
            log.error("删除分段失败：chunk_ids 列表为空或格式不正确");
            return ApiResp.error(400, "chunk_ids 列表不能为空");
        }
        
        RagFlowResponse<?> response = ragBaseService.deleteChunks(datasetId, documentId, request);
        
        if (response != null && response.getCode() == 0) {
            return ApiResp.success("分段删除成功", response.getData());
        } else {
            return ApiResp.error(response != null ? response.getCode() : 500, 
                                 response != null ? response.getMessage() : "分段删除失败");
        }
    }

    @PutMapping("/datasets/{datasetId}/documents/{documentId}/chunks/{chunkId}")
    @Operation(summary = "更新文档中的特定分段", 
               description = "更新指定知识库、指定文档中特定分段的内容或配置，如文本、关键词或可用状态。")
    public ApiResp<?> updateChunkInDocument(
            @Parameter(name = "datasetId", description = "目标知识库的ID", required = true)
            @PathVariable String datasetId,
            @Parameter(name = "documentId", description = "目标文档的ID", required = true)
            @PathVariable String documentId,
            @Parameter(name = "chunkId", description = "要更新的目标分段的ID", required = true)
            @PathVariable String chunkId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "更新分段的请求体。仅提供需要修改的字段。", 
                required = true,
                content = @Content(schema = @Schema(implementation = UpdateChunkRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody UpdateChunkRequest request) {
        
        log.info("更新文档分段，知识库ID: {}, 文档ID: {}, 分段ID: {}, 请求: {}", 
                 datasetId, documentId, chunkId, request);
        
        if (request == null) {
             return ApiResp.error(400, "请求体不能为空");
        }
        
        RagFlowResponse<?> response = ragBaseService.updateChunkInDocument(datasetId, documentId, chunkId, request);
        
        if (response != null && response.getCode() == 0) {
            return ApiResp.success("分段更新成功", response.getData());
        } else {
            return ApiResp.error(response != null ? response.getCode() : 500, 
                                 response != null ? response.getMessage() : "分段更新失败");
        }
    }

    /**
     * 上传QA对到知识库
     */
    @PostMapping("/datasets/{datasetId}/qa")
    @Operation(summary = "上传QA对到知识库", description = "将问答对上传到指定知识库，自动创建CSV文件并设置为QA类型解析")
    public ApiResp<?> uploadQAToDataset(
            @Parameter(description = "知识库ID", required = true, example = "dataset_id_abc") 
            @PathVariable String datasetId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "QA对列表", 
                required = true,
                content = @Content(array = @ArraySchema(schema = @Schema(implementation = QAUploadRequest.class))))
            @org.springframework.web.bind.annotation.RequestBody List<QAUploadRequest> qaList) {
        
        log.info("上传QA对到知识库，知识库ID: {}, QA对数量: {}", datasetId, qaList != null ? qaList.size() : 0);
        
        // 参数验证
        if (qaList == null || qaList.isEmpty()) {
            return ApiResp.error(400, "QA对列表不能为空");
        }
        
        // 验证每个QA对的内容
        for (int i = 0; i < qaList.size(); i++) {
            QAUploadRequest qa = qaList.get(i);
            if (qa == null) {
                return ApiResp.error(400, "第" + (i + 1) + "个QA对不能为空");
            }
            if (qa.getQuestion() == null || qa.getQuestion().trim().isEmpty()) {
                return ApiResp.error(400, "第" + (i + 1) + "个QA对的问题不能为空");
            }
            if (qa.getAnswer() == null || qa.getAnswer().trim().isEmpty()) {
                return ApiResp.error(400, "第" + (i + 1) + "个QA对的答案不能为空");
            }
        }
        
        try {
            List<QAUploadResponse> uploadedQAs = ragBaseService.uploadQAToDataset(datasetId, qaList);
            
            log.info("QA对上传完成，知识库ID: {}, 成功上传: {}", datasetId, uploadedQAs.size());
            
            return ApiResp.success("QA对上传成功", uploadedQAs);
            
        } catch (Exception e) {
            log.error("上传QA对时发生异常，知识库ID: {}", datasetId, e);
            return ApiResp.error(500, "上传QA对时发生内部错误: " + e.getMessage());
        }
    }

    /**
     * 查询多个知识库中的QA对
     */
    @PostMapping("/datasets/qa/query")
    @Operation(summary = "查询QA对", description = "根据提供的知识库ID列表，查询相关QA对数据")
    public ApiResp<?> queryQAPairs(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "QA对查询请求",
                required = true,
                content = @Content(schema = @Schema(implementation = QAQueryRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody QAQueryRequest request) {
        
        log.info("查询QA对，请求: {}", request);
        
        // 参数验证
        if (request == null || request.getDataset_ids() == null || request.getDataset_ids().isEmpty()) {
            return ApiResp.error(400, "知识库ID列表不能为空");
        }
        
        // 验证知识库ID格式
        for (String datasetId : request.getDataset_ids()) {
            if (datasetId == null || datasetId.trim().isEmpty()) {
                return ApiResp.error(400, "知识库ID不能为空");
            }
        }
        
        try {
            // 根据是否有搜索条件选择调用不同的方法
            QAPairListResponse qaPairs;
//            if (request.getKeywords() != null && !request.getKeywords().trim().isEmpty()) {
//                // 有搜索条件，使用带搜索的方法
//                qaPairs = ragBaseService.queryQAPairsWithSearch(
//                    request.getDataset_ids(),
//                    request.getKeywords(),
//                    request.getPage(),
//                    request.getPage_size()
//                );
//                log.info("QA对搜索查询完成，关键词: {}, 总QA对数: {}, 当前页: {}, 返回条数: {}",
//                         request.getKeywords(), qaPairs.getTotal(), qaPairs.getPage(), qaPairs.getData().size());
//            } else {
                // 无搜索条件，使用原有方法
                qaPairs = ragBaseService.queryQAPairsSimplified(request.getDataset_ids());
                log.info("QA对查询完成，总QA对数: {}", qaPairs.getTotal());
//            }
            
            return ApiResp.success("QA对查询成功", qaPairs);
            
        } catch (Exception e) {
            log.error("查询QA对时发生异常，知识库ID列表: {}",
                     request.getDataset_ids(), e);
            return ApiResp.error(500, "查询QA对时发生内部错误: " + e.getMessage());
        }
    }

    /**
     * 删除知识库中的QA对
     */
    @DeleteMapping("/datasets/{datasetId}/qa")
    @Operation(summary = "删除知识库中的QA对", description = "删除指定知识库中的一个或多个QA对")
    public ApiResp<?> deleteQAPairs(
            @Parameter(description = "知识库ID", required = true, example = "dataset_id_abc") 
            @PathVariable String datasetId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "QA对删除请求，包含要删除的QA对ID列表", 
                required = true,
                content = @Content(schema = @Schema(implementation = QADeleteRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody QADeleteRequest request) {
        
        // 参数验证
        if (request == null || request.getQa_ids() == null || request.getQa_ids().isEmpty()) {
            log.error("删除QA对失败：QA对ID列表为空，知识库ID: {}", datasetId);
            return ApiResp.error(400, "QA对ID列表不能为空");
        }
        
        log.info("删除QA对，知识库ID: {}, QA对IDs: {}", datasetId, request.getQa_ids());
        
        try {
            RagFlowResponse<?> response = ragBaseService.deleteQAPairs(datasetId, request.getQa_ids());
            
            if (response != null && response.getCode() != null && response.getCode() == 0) {
                return ApiResp.success("QA对删除成功", response.getData());
            } else {
                String errorMessage = response != null ? response.getMessage() : "未知错误";
                log.error("QA对删除失败，知识库ID: {}, 错误: {}", datasetId, errorMessage);
                return ApiResp.error(-1, "QA对删除失败: " + errorMessage);
            }
        } catch (Exception e) {
            log.error("删除QA对时发生异常，知识库ID: {}, QA对IDs: {}", datasetId, request.getQa_ids(), e);
            return ApiResp.error(-1, "删除QA对时发生异常: " + e.getMessage());
        }
    }

    /**
     * 更新知识库中的QA对
     */
    @PutMapping("/datasets/{datasetId}/qa/{qaId}")
    @Operation(summary = "更新知识库中的QA对", description = "更新指定知识库中指定QA对的问题和答案")
    public ApiResp<?> updateQAPair(
            @Parameter(description = "知识库ID", required = true, example = "dataset_id_abc") 
            @PathVariable String datasetId,
            @Parameter(description = "QA对ID", required = true, example = "qa_id_123") 
            @PathVariable String qaId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "QA对更新请求，包含新的问题和答案", 
                required = true,
                content = @Content(schema = @Schema(implementation = QAUpdateRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody QAUpdateRequest request) {
        
        // 参数验证
        if (request == null) {
            log.error("更新QA对失败：请求体为空，知识库ID: {}, QA对ID: {}", datasetId, qaId);
            return ApiResp.error(400, "请求体不能为空");
        }
        
        if (request.getQuestion() == null || request.getQuestion().trim().isEmpty()) {
            log.error("更新QA对失败：问题为空，知识库ID: {}, QA对ID: {}", datasetId, qaId);
            return ApiResp.error(400, "问题不能为空");
        }
        
        if (request.getAnswer() == null || request.getAnswer().trim().isEmpty()) {
            log.error("更新QA对失败：答案为空，知识库ID: {}, QA对ID: {}", datasetId, qaId);
            return ApiResp.error(400, "答案不能为空");
        }
        
        log.info("更新QA对，知识库ID: {}, QA对ID: {}, 问题: {}, 答案: {}", 
                 datasetId, qaId, request.getQuestion(), request.getAnswer());
        
        try {
            RagFlowResponse<?> response = ragBaseService.updateQAPair(datasetId, qaId, 
                                                                     request.getQuestion(), request.getAnswer());
            
            if (response != null && response.getCode() != null && response.getCode() == 0) {
                log.info("QA对更新成功，知识库ID: {}, QA对ID: {}", datasetId, qaId);
                return ApiResp.success("QA对更新成功", response.getData());
            } else {
                String errorMessage = response != null ? response.getMessage() : "未知错误";
                log.error("QA对更新失败，知识库ID: {}, QA对ID: {}, 错误: {}", datasetId, qaId, errorMessage);
                return ApiResp.error(-1, "QA对更新失败: " + errorMessage);
            }
        } catch (Exception e) {
            log.error("更新QA对时发生异常，知识库ID: {}, QA对ID: {}", datasetId, qaId, e);
            return ApiResp.error(-1, "更新QA对时发生异常: " + e.getMessage());
        }
    }

    /**
     * 单个文档上传后触发解析（排除QA文档）
     */
    private void triggerDocumentParsingIfNeeded(String datasetId, RagFlowResponse<?> uploadResponse, String fileName) {
        try {
            // 排除QA文档，避免与QA上传逻辑冲突
            if ("QA数据集.csv".equals(fileName)) {
                log.debug("跳过QA文档的自动解析，文件名: {}", fileName);
                return;
            }
            
            // 提取文档ID
            String documentId = extractDocumentIdFromResponse(uploadResponse);
            if (documentId == null) {
                log.warn("无法从上传响应中提取文档ID，跳过自动解析，文件名: {}", fileName);
                return;
            }
            
            // 构建解析请求
            com.unnet.api.dto.ragflow.ParseDocumentsRequest parseRequest = 
                new com.unnet.api.dto.ragflow.ParseDocumentsRequest();
            parseRequest.setDocument_ids(java.util.Arrays.asList(documentId));
            
            // 触发文档解析
            RagFlowResponse<?> parseResponse = ragBaseService.parseDocuments(datasetId, parseRequest);
            if (parseResponse != null && parseResponse.getCode() != null && parseResponse.getCode() == 0) {
                log.info("文档自动解析触发成功，知识库ID: {}, 文档ID: {}, 文件名: {}", 
                        datasetId, documentId, fileName);
            } else {
                log.warn("文档自动解析触发失败，知识库ID: {}, 文档ID: {}, 文件名: {}, 响应: {}", 
                        datasetId, documentId, fileName, parseResponse);
            }
        } catch (Exception e) {
            log.warn("文档自动解析触发异常，知识库ID: {}, 文件名: {}, 错误: {}", 
                    datasetId, fileName, e.getMessage());
        }
    }

    /**
     * 批量文档上传后触发解析（排除QA文档）
     */
    private void triggerBatchDocumentParsingIfNeeded(String datasetId, RagFlowResponse<?> uploadResponse, List<String> fileNames) {
        try {
            // 提取所有文档ID
            List<String> documentIds = extractDocumentIdsFromBatchResponse(uploadResponse);
            if (documentIds == null || documentIds.isEmpty()) {
                log.warn("无法从批量上传响应中提取文档ID，跳过自动解析，文件数量: {}", fileNames.size());
                return;
            }
            
            // 过滤掉QA文档ID（根据文件名判断）
            List<String> nonQADocumentIds = new ArrayList<>();
            for (int i = 0; i < Math.min(documentIds.size(), fileNames.size()); i++) {
                String fileName = fileNames.get(i);
                String documentId = documentIds.get(i);
                
                if (!"QA数据集.csv".equals(fileName)) {
                    nonQADocumentIds.add(documentId);
                } else {
                    log.debug("跳过QA文档的自动解析，文档ID: {}, 文件名: {}", documentId, fileName);
                }
            }
            
            if (nonQADocumentIds.isEmpty()) {
                log.info("所有上传的文档都是QA文档，跳过自动解析");
                return;
            }
            
            // 构建批量解析请求
            com.unnet.api.dto.ragflow.ParseDocumentsRequest parseRequest = 
                new com.unnet.api.dto.ragflow.ParseDocumentsRequest();
            parseRequest.setDocument_ids(nonQADocumentIds);
            
            // 触发批量文档解析
            RagFlowResponse<?> parseResponse = ragBaseService.parseDocuments(datasetId, parseRequest);
            if (parseResponse != null && parseResponse.getCode() != null && parseResponse.getCode() == 0) {
                log.info("批量文档自动解析触发成功，知识库ID: {}, 文档数量: {}, 文档IDs: {}", 
                        datasetId, nonQADocumentIds.size(), nonQADocumentIds);
            } else {
                log.warn("批量文档自动解析触发失败，知识库ID: {}, 文档数量: {}, 文档IDs: {}, 响应: {}", 
                        datasetId, nonQADocumentIds.size(), nonQADocumentIds, parseResponse);
            }
        } catch (Exception e) {
            log.warn("批量文档自动解析触发异常，知识库ID: {}, 文件数量: {}, 错误: {}", 
                    datasetId, fileNames.size(), e.getMessage());
        }
    }

    /**
     * 从上传响应中提取文档ID
     */
    private String extractDocumentIdFromResponse(RagFlowResponse<?> response) {
        try {
            Object data = response.getData();
            
            // 处理FilteredDocumentResponse对象
            if (data instanceof com.unnet.api.dto.ragflow.FilteredDocumentResponse) {
                com.unnet.api.dto.ragflow.FilteredDocumentResponse doc = 
                    (com.unnet.api.dto.ragflow.FilteredDocumentResponse) data;
                return doc.getId();
            }
            // 处理FilteredDocumentResponse列表
            else if (data instanceof List) {
                List<?> dataList = (List<?>) data;
                if (!dataList.isEmpty() && dataList.get(0) instanceof com.unnet.api.dto.ragflow.FilteredDocumentResponse) {
                    com.unnet.api.dto.ragflow.FilteredDocumentResponse doc = 
                        (com.unnet.api.dto.ragflow.FilteredDocumentResponse) dataList.get(0);
                    return doc.getId();
                }
            }
            // 兼容原始Map格式
            else if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                return (String) dataMap.get("id");
            }
            
            log.warn("无法识别的上传响应数据格式: {}", data != null ? data.getClass().getSimpleName() : "null");
        } catch (Exception e) {
            log.error("提取文档ID失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 从批量上传响应中提取文档ID列表
     */
    private List<String> extractDocumentIdsFromBatchResponse(RagFlowResponse<?> response) {
        try {
            Object data = response.getData();
            List<String> documentIds = new ArrayList<>();
            
            // 处理FilteredDocumentResponse列表
            if (data instanceof List) {
                List<?> dataList = (List<?>) data;
                for (Object item : dataList) {
                    if (item instanceof com.unnet.api.dto.ragflow.FilteredDocumentResponse) {
                        com.unnet.api.dto.ragflow.FilteredDocumentResponse doc = 
                            (com.unnet.api.dto.ragflow.FilteredDocumentResponse) item;
                        documentIds.add(doc.getId());
                    } else if (item instanceof Map) {
                        // 兼容原始Map格式
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        String id = (String) itemMap.get("id");
                        if (id != null) {
                            documentIds.add(id);
                        }
                    }
                }
                return documentIds;
            }
            // 处理单个FilteredDocumentResponse对象
            else if (data instanceof com.unnet.api.dto.ragflow.FilteredDocumentResponse) {
                com.unnet.api.dto.ragflow.FilteredDocumentResponse doc = 
                    (com.unnet.api.dto.ragflow.FilteredDocumentResponse) data;
                documentIds.add(doc.getId());
                return documentIds;
            }
            // 兼容原始Map格式
            else if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                String id = (String) dataMap.get("id");
                if (id != null) {
                    documentIds.add(id);
                }
                return documentIds;
            }
            
            log.warn("无法识别的批量上传响应数据格式: {}", data != null ? data.getClass().getSimpleName() : "null");
        } catch (Exception e) {
            log.error("提取批量上传文档ID失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 复制对话配置
     */
    @PostMapping("/datasets/dialog/copy-config")
    @Operation(summary = "复制对话配置", 
               description = "从源对话记录复制配置到指定的目标对话记录。" +
                           "包含字段：prompt_config、similarity_threshold、vector_similarity_weight、top_n、top_k、rerank_id。" +
                           "直接使用对话ID，操作更加精确和简单。")
    public ApiResp<?> copyDialogConfig(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "复制对话配置请求", 
                required = true,
                content = @Content(schema = @Schema(implementation = com.unnet.api.dto.ragflow.CopyDialogConfigRequest.class)))
            @org.springframework.web.bind.annotation.RequestBody com.unnet.api.dto.ragflow.CopyDialogConfigRequest request) {
        
        log.info("复制对话配置请求: {}", request);
        
        // 参数验证
        if (request == null) {
            return ApiResp.error(400, "请求体不能为空");
        }
        
        if (request.getSourceDialogId() == null || request.getSourceDialogId().trim().isEmpty()) {
            return ApiResp.error(400, "源对话ID不能为空");
        }
        
        if (request.getTargetDialogIds() == null || request.getTargetDialogIds().isEmpty()) {
            return ApiResp.error(400, "目标对话ID列表不能为空");
        }
        
        try {
            // 调用Service层执行复制操作
            ApiResp<?> result = ragBaseService.copyDialogConfig(
                    request.getSourceDialogId(), 
                    request.getTargetDialogIds()
            );
            
            return result;
            
        } catch (Exception e) {
            log.error("复制对话配置时发生异常", e);
            return ApiResp.error(500, "复制失败: " + e.getMessage());
        }
    }

} 