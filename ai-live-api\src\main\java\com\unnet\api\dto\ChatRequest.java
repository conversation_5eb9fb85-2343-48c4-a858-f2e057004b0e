package com.unnet.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 聊天请求DTO
 */
@Data
@Schema(description = "聊天请求")
public class ChatRequest {

    @Schema(description = "消息列表")
    @NotNull(message = "消息列表不能为空")
    private List<ChatMessage> messages;

    @Schema(description = "是否流式输出", defaultValue = "false")
    private Boolean stream = false;

    @Schema(description = "最大token数", defaultValue = "10240")
    @JsonProperty("max_tokens")
    private Integer maxTokens;

    @Schema(description = "温度参数", defaultValue = "0.7")
    private Double temperature = 0.7;

    @Schema(description = "top_p参数", defaultValue = "0.9")
    @JsonProperty("top_p")
    private Double topP = 0.9;

    /**
     * 聊天消息
     */
    @Data
    @Schema(description = "聊天消息")
    public static class ChatMessage {
        
        @Schema(description = "角色", allowableValues = {"system", "user", "assistant"})
        @NotBlank(message = "角色不能为空")
        private String role;

        @Schema(description = "消息内容")
        @NotBlank(message = "消息内容不能为空")
        private String content;
    }
} 