package com.unnet.api.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.MalformedURLException;
import java.util.List;


public interface VoiceApiService {

    //生成音频
    JSONObject generateAudio(List<Object> data) throws Exception;

    //音频流式访问
    String getAudio(String eventId) throws Exception;

    //m3u8下载
    ResponseEntity<byte[]> getM3U8(String event_id, String path) throws Exception;

    //音频片段下载
    ResponseEntity<byte[]>  getPartAudio(String event_id, String path,String uuid_aac) throws Exception;

    //完整音频下载
    ResponseEntity<byte[]>  getFullAudio(String event_id, String path) throws Exception;

}
