package com.unnet.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * ASR任务创建请求
 */
@Data
@Schema(description = "ASR任务创建请求")
public class AsrTaskCreateRequest {
    
    @NotNull(message = "音频文件不能为空")
    @Schema(description = "音频文件", required = true)
    private MultipartFile file;
} 