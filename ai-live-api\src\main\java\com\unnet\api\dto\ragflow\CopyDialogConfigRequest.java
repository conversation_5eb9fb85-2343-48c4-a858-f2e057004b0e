package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "复制对话配置请求 - 包含prompt_config、similarity_threshold、vector_similarity_weight、top_n、top_k、rerank_id等字段")
public class CopyDialogConfigRequest {

    @NotBlank(message = "源对话ID不能为空")
    @Schema(description = "源对话ID，将从这条记录复制所有相关配置字段", required = true, example = "dialog_source_123")
    private String sourceDialogId;

    @NotNull(message = "目标对话ID列表不能为空")
    @NotEmpty(message = "目标对话ID列表不能为空")
    @Schema(description = "目标对话ID列表，直接指定要复制配置的对话记录ID", required = true, example = "[\"dialog_target_001\", \"dialog_target_002\"]")
    private List<String> targetDialogIds;
} 