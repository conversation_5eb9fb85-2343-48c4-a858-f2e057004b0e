package com.unnet.api.dto.internal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
// import java.util.List; // Not currently needed for the fields we extract

@Data
public class CreateSessionInternalResponse {
    private int code;
    private DataDetails data;

    @lombok.Data
    public static class DataDetails {
        // This is the session_id that we need
        private String id; 
        
        @JsonProperty("chat_id")
        private String chatId;
        
        private String name;
        // Other fields like messages, create_date, update_date, create_time, update_time 
        // can be added here if needed in the future.
    }
} 