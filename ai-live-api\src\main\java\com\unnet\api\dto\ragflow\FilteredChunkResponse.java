package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "过滤后的分段响应数据")
public class FilteredChunkResponse {

    @Schema(description = "分段是否可用", example = "true")
    private Boolean available;

    @Schema(description = "分段内容", example = "这是分段的文本内容")
    private String content;

    @Schema(description = "文档知识库ID", example = "1.txt")
    private String docnm_kwd;

    @Schema(description = "分段ID", example = "5f2042e5512042ac110006")
    private String id;

    @Schema(description = "重要关键词列表")
    private Object important_kwd;

    @Schema(description = "位置信息")
    private Object positions;
} 