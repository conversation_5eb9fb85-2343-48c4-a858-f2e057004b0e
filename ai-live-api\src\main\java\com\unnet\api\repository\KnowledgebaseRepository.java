package com.unnet.api.repository;

import com.unnet.api.entity.KnowledgebaseEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 知识库数据访问层
 */
@Repository
public interface KnowledgebaseRepository extends JpaRepository<KnowledgebaseEntity, String> {
    
    /**
     * 更新知识库的统计字段
     */
    @Modifying
    @Transactional
    @Query("UPDATE KnowledgebaseEntity k SET k.docNum = :docNum, k.tokenNum = :tokenNum, k.chunkNum = :chunkNum WHERE k.id = :id")
    int updateStatistics(@Param("id") String id, 
                        @Param("docNum") Integer docNum, 
                        @Param("tokenNum") Long tokenNum, 
                        @Param("chunkNum") Integer chunkNum);
} 