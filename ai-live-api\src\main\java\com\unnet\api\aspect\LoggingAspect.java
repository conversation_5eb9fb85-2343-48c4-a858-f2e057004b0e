package com.unnet.api.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import javax.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class LoggingAspect {

    private final ObjectMapper objectMapper;

    /**
     * 定义切点: 所有controller包下的所有方法
     */
    @Pointcut("execution(* com.example.api.controller..*.*(..))")
    public void controllerMethods() {}

    /**
     * 环绕通知: 记录请求日志、响应内容和执行时间
     */
    @Around("controllerMethods()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            
            // 记录请求内容
            log.debug("请求信息 - 方法: {}, 路径: {}, IP: {}, 类方法: {}.{}, 参数: {}",
                    request.getMethod(),
                    request.getRequestURI(),
                    request.getRemoteAddr(),
                    joinPoint.getSignature().getDeclaringTypeName(),
                    joinPoint.getSignature().getName(),
                    Arrays.toString(joinPoint.getArgs()));
        }
        
        // 执行目标方法
        Object result = joinPoint.proceed();
        
        // 计算执行时间
        long executionTime = System.currentTimeMillis() - startTime;
        
        // 记录响应内容和执行时间
        if (result != null) {
            // 为避免日志过大，这里可能需要限制响应数据的日志长度
            String response = objectMapper.writeValueAsString(result);
            if (response.length() > 1000) {
                response = response.substring(0, 1000) + "...";
            }
            log.debug("响应信息 - 执行时间: {}ms, 响应数据: {}", executionTime, response);
        } else {
            log.debug("响应信息 - 执行时间: {}ms, 无响应数据", executionTime);
        }
        
        return result;
    }

    /**
     * 异常通知: 记录异常日志
     */
    @AfterThrowing(pointcut = "controllerMethods()", throwing = "exception")
    public void logAfterThrowing(JoinPoint joinPoint, Exception exception) {
        // 记录异常信息
        log.error("方法: {}.{} 抛出异常: {}",
                joinPoint.getSignature().getDeclaringTypeName(),
                joinPoint.getSignature().getName(),
                exception.getMessage());
        
        // 如果需要记录详细堆栈信息
        if (log.isDebugEnabled()) {
            log.debug("异常堆栈跟踪: ", exception);
        }
    }
} 