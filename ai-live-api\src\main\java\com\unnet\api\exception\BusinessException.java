package com.unnet.api.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * 业务异常类，用于抛出业务逻辑相关的异常
 */
@Getter
public class BusinessException extends RuntimeException {
    
    private final HttpStatus status;
    
    public BusinessException(String message) {
        this(HttpStatus.BAD_REQUEST, message);
    }
    
    public BusinessException(HttpStatus status, String message) {
        super(message);
        this.status = status;
    }
    
    public BusinessException(HttpStatus status, String message, Throwable cause) {
        super(message, cause);
        this.status = status;
    }
} 