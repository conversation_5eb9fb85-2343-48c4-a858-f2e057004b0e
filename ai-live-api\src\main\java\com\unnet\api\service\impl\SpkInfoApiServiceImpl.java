package com.unnet.api.service.impl;

import com.unnet.api.entity.Voice;
import com.unnet.api.entity.VoiceData;
import com.unnet.api.mapper.SpkInfoApiMapper;
import com.unnet.api.service.SpkInfoApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.unnet.api.config.RouterConfig;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Service
@Slf4j
public class SpkInfoApiServiceImpl implements SpkInfoApiService {

    @Resource
    private SpkInfoApiMapper spkInfoApiMapper;

    @Resource
    private RouterConfig routerConfig;

    //系统用户id
    private static final String systemId = "system_id_001";

    //查找音色
    @Override
    public List<Map<String, String>> find(String userId) {
        List<VoiceData> voiceList = spkInfoApiMapper.findById(userId);

        //用户的音色信息
        List<Map<String,String>> resultList = new ArrayList<>();
        for(VoiceData voice:voiceList){
            Map<String,String> resultMap = new HashMap<>();
            resultMap.put("spk_name",voice.getSpkName());
            resultMap.put("spk_gender",voice.getSpkGender());
            resultMap.put("spk_desc",voice.getSpkDesc());
            resultList.add(resultMap);
        }

        //系统的音色信息
        List<VoiceData> voiceListSystem = spkInfoApiMapper.findById(systemId);
        for(VoiceData voice:voiceListSystem){
            Map<String,String> resultMap = new HashMap<>();
            resultMap.put("spk_name",voice.getSpkName());
            resultMap.put("spk_gender",voice.getSpkGender());
            resultMap.put("spk_desc",voice.getSpkDesc());
            resultList.add(resultMap);
        }

        return resultList;
    }


    /**
     * 上传音色；tts/test存储试听音频；tts/prompt存储原始音频
     * @param voice
     * @return
     * @throws IOException
     */
    @Override
    public Map<String, String> upload(Voice voice) throws IOException {
        //获取合适路由
        String API_URL = routerConfig.findIp();
        //修改最后一位端口 0 -> 1  调用TTS-Server-FastApi服务端口
        String modifiedUrl = API_URL.replaceAll("\\d(?!.*\\d)", "1");

        // 设置请求头
        String boundary = "Boundary-" + UUID.randomUUID().toString();
        String LINE_END = "\r\n";
        // 发送 POST 请求
        HttpURLConnection connection = (HttpURLConnection) new URL(modifiedUrl + "/tts/api/generate_test_audio").openConnection();
        connection.setRequestMethod("POST");
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setUseCaches(false);
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

        // 构建请求体
        try (OutputStream outputStream = connection.getOutputStream();
             PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true)) {
            String fileName = voice.getAudioFileUrl().getOriginalFilename();
            // 1. 添加音频文件
            writer.append("--").append(boundary).append(LINE_END);
            writer.append("Content-Disposition: form-data; name=\"audio_file\"; filename=\"")
                    .append(fileName).append("\"").append(LINE_END);
            writer.append("Content-Type: audio/"+getFileExtension(fileName)).append(LINE_END);
            writer.append(LINE_END).flush();

            // 写入音频文件内容
            try (InputStream fileInputStream = voice.getAudioFileUrl().getInputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
            writer.append(LINE_END).flush();

            // 添加其他表单字段
            addFormField(writer, boundary, "prompt_text", voice.getPromptText());
            addFormField(writer, boundary, "tts_text", "这是一段试听音频，用于演示"+voice.getSpkName()+"的声音特征！");

            // 3. 结束标记
            writer.append("--").append(boundary).append("--").append(LINE_END).flush();
        }

        // 获取响应
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            // 创建测试音频保存目录
            String testDirPath = Paths.get("").toAbsolutePath().toString() + "/tts/test/";
            log.info("testDirPath:{}", testDirPath);

            File testDir = new File(testDirPath);
            if (!testDir.exists()) {
                testDir.mkdirs();
            }

            // 生成测试音频文件名
            String testFilename = voice.getUserId() + "-" + voice.getSpkName() + ".wav";
            String testPath = "tts/test/" + testFilename;
            File testFile = new File(testDirPath + testFilename);

            // 保存测试音频
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(testFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            //存储音色到本地
            String uuid = UUID.randomUUID().toString();
            String originalFilename = voice.getAudioFileUrl().getOriginalFilename();
            String filepath = "tts/prompt/" + uuid + "-" + originalFilename;
            String path = Paths.get("").toAbsolutePath().toString() + "/" +filepath;

            // 确保目标目录存在
            File directory = new File(Paths.get("").toAbsolutePath().toString()+ "/tts/prompt/");
            log.info("directory:{}",directory);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 将文件存储到本地
            File destFile = new File(path);
            voice.getAudioFileUrl().transferTo(destFile);

            //存储记录到数据库
            VoiceData voicedata = new VoiceData();
            voicedata.setSpkDesc(voice.getSpkDesc());
            voicedata.setSpkName(voice.getSpkName());
            voicedata.setSpkGender(voice.getSpkGender());
            voicedata.setPromptText(voice.getPromptText());
            voicedata.setUserId(voice.getUserId());
            voicedata.setFilePath(filepath);
            voicedata.setTestPath(testPath);
            int exist = spkInfoApiMapper.exist(voice.getUserId(),voice.getSpkName());
            if(exist==1) {
                spkInfoApiMapper.update(voicedata);
            } else {
                spkInfoApiMapper.insert(voicedata);
            }
            Map<String,String> resultMap = new HashMap<>();
            resultMap.put("spk_name", voice.getSpkName());
            return resultMap;
        } else {
            throw new IOException("服务器返回错误: " + responseCode);
        }
    }

    // 辅助方法：文件扩展名提取
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    // 辅助方法：添加表单字段
    private void addFormField(PrintWriter writer, String boundary, String fieldName, String fieldValue) {
        writer.append("--" + boundary).append("\r\n");
        writer.append("Content-Disposition: form-data; name=\"" + fieldName + "\"").append("\r\n");
        writer.append("\r\n");
        writer.append(fieldValue).append("\r\n").flush();
    }

    //删除音色
    @Override
    public void delete(String spkName, String userId) {
        // 1. 查询数据库记录
        VoiceData voiceData = spkInfoApiMapper.find(userId, spkName);
        if (voiceData == null) {
            log.info("未找到用户[{}]的音色[{}]记录", userId, spkName);
            return;
        }

        // 2. 获取文件路径
        String filePath = voiceData.getFilePath();
        String testPath = voiceData.getTestPath();

        // 3. 删除文件
        try {
            String baseDir = Paths.get("").toAbsolutePath().toString()+"\\";

            // 删除原音频文件
            if (filePath!=null && !"".equals(filePath)) {
                Path absolutePath = Paths.get(baseDir, filePath).normalize();
                Files.deleteIfExists(absolutePath);
            }

            // 删除测试文件
            if (testPath!=null && !"".equals(testPath)) {
                Path absolutePath = Paths.get(baseDir, testPath).normalize();
                Files.deleteIfExists(absolutePath);
            }
        } catch (IOException e) {
            throw new RuntimeException("删除音色文件失败", e);
        }

        // 4. 删除数据库记录
        spkInfoApiMapper.delete(spkName, userId);
    }

    //下载提示音频
    @Override
    public ResponseEntity<byte[]> download(String spkName, String userid , int model) throws Exception {
        String type = "filePath";
        if( model == 2) {
            type = "testPath";
        }
        String filePath = routerConfig.getInfo(spkName,userid,type);

        if(filePath == null || filePath.isEmpty()) {
            VoiceData voiceData = spkInfoApiMapper.find(userid,spkName);
            //若找不到个人主播信息，则是系统主播信息
            if(voiceData==null) {
                voiceData = spkInfoApiMapper.find(systemId,spkName);
            }
            if(voiceData!=null) {
                if(model == 1) {
                    filePath = voiceData.getFilePath();
                } else {
                    filePath = voiceData.getTestPath();
                }
                routerConfig.storeInfo(spkName, voiceData.getUserId(), filePath, type);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            }
        }

        // 检查文件是否存在
        File audioFile = new File(filePath);
        if (!audioFile.exists()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }

        // 读取音频文件的字节数据
        byte[] audioData = Files.readAllBytes(audioFile.toPath());


        String safeFileName = audioFile.getName().replaceAll("[^\\w.-]", "_"); // 替换非法字符
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentLength(audioData.length);
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + safeFileName);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("audio/wav"))
                .body(audioData);
    }

    //提示词下载
    @Override
    public String downloadTips(String spkName, String userid) throws Exception {
        String tips = null;
        tips= routerConfig.getInfo(spkName,userid,"tips");
        if(tips == null || tips.isEmpty()){
            VoiceData voiceData = spkInfoApiMapper.find(userid,spkName);
            //若找不到个人主播信息，则是系统主播信息
            if(voiceData==null) {
                voiceData = spkInfoApiMapper.find(systemId,spkName);
            }
            if(voiceData!=null) {
                tips = voiceData.getPromptText();
                routerConfig.storeInfo(spkName, userid, tips, "tips");
            }
        }
        return tips;
    }
}
