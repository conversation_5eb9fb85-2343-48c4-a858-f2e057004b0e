package com.unnet.api.dto.ragflow;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RagFlow聊天助手创建请求对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatAssistantRequest {

    /**
     * 聊天助手名称
     */
    private String name;
    
    /**
     * 关联的知识库ID列表
     */
    private List<String> dataset_ids;

    /**
     * 提示引擎相关配置
     */
    private Map<String, Object> prompt = new HashMap<>();
} 