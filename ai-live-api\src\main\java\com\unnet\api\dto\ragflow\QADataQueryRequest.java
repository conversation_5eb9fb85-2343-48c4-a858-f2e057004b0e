package com.unnet.api.dto.ragflow;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA文档中查询QA对")
public class QADataQueryRequest {

    // 必填字段
    private String question; // 用户查询或查询关键字

    // 可选字段
    private List<String> dataset_ids; // 要搜索的数据集的ID
    private List<String> document_ids; // 要搜索的文档的ID
    private Integer page = 1; // 指定显示块的页面，默认为1
    private Integer page_size = 30; // 每个页面上的最大块数，默认为30
    private Double similarity_Threshold = 0.96; // 最低相似度分数，默认为0.2
    private Double vector_similarity_weight = 0.3; // 向量余弦相似度的权重，默认为0.3
    private Integer top_k = 1024; // 参与向量余弦计算的块数，默认为1024
    private String rerank_id; // rerank模型的ID
    private Boolean keyword = false; // 是否启用基于关键字的匹配，默认为false
    private Boolean highlight = false; // 是否启用匹配词的高亮显示，默认为false

}
