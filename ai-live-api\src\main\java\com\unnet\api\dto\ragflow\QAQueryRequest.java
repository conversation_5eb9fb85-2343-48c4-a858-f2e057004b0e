package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA对查询请求")
public class QAQueryRequest {

    @Schema(description = "知识库ID列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"f6a4d3023aa811f08c930242ac110006\"]")
    private List<String> dataset_ids;

//    @Schema(description = "关键词，用于搜索QA对的问题或答案内容", example = "比亚迪")
//    private String keywords;
//
//    @Schema(description = "页码，默认为1", example = "1")
//    private Integer page;
//
//    @Schema(description = "每页条数，默认为50", example = "10")
//    private Integer page_size;
} 