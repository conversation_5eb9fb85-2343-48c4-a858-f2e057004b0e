package com.unnet.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "请求，用于与带会话管理的聊天助手进行对话")
public class SessionChatRequest {

    @NotBlank(message = "datasetId 不能为空")
    @Schema(description = "知识库ID", required = true)
    private String datasetId;

    @Schema(description = "会话ID (可选, 如果不提供或为空，将创建新会话)")
    private String sessionId;

    @NotBlank(message = "question 不能为空")
    @Schema(description = "用户的问题或消息", required = true)
    private String question;

    @Schema(description = "是否流式输出", defaultValue = "false")
    private Boolean stream = false;

    @Schema(description = "直播风格与个性（主要用于控制输出内容的长短）", hidden = true)
    private String liveStyle;

    @Schema(description = "本场直播上下文（如当前时间、直播车型等）", hidden = true)
    private String context;
} 