package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA对列表响应")
public class QAPairListResponse {

    @Schema(description = "QA对总数", example = "25")
    private Integer total;

    @Schema(description = "QA对数据列表")
    private List<QAPairResponse> data;

//    @Schema(description = "当前页码", example = "1")
//    private Integer page;
//
//    @Schema(description = "每页条数", example = "10")
//    private Integer page_size;
//
//    @Schema(description = "总页数", example = "3")
//    private Integer total_pages;
//
//    // 兼容原有构造函数
//    public QAPairListResponse(Integer total, List<QAPairResponse> data) {
//        this.total = total;
//        this.data = data;
//        this.page = 1;
//        this.page_size = total;
//        this.total_pages = 1;
//    }
} 