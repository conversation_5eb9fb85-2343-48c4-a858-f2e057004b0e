package com.unnet.api.dto.ragflow;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * QA上传请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA上传请求")
public class QAUploadRequest {
    
    @Schema(description = "问题", required = true, example = "比亚迪秦2022款的售价是多少？")
    private String question;
    
    @Schema(description = "答案", required = true, example = "大概11.2万")
    private String answer;
} 