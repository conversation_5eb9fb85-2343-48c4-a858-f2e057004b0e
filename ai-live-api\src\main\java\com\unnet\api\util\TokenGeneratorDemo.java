package com.unnet.api.util;

/**
 * TokenGenerator使用示例
 */
public class TokenGeneratorDemo {

    public static void main(String[] args) {
        // 生成默认API Token
        String token1 = TokenGenerator.generateApiToken();
        System.out.println("默认API Token: " + token1);
        
        // 使用UUID生成API Token
        String token2 = TokenGenerator.generateApiTokenWithUUID();
        System.out.println("UUID生成的API Token: " + token2);
        
        // 生成指定长度的API Token（不含前缀长度）
        String token3 = TokenGenerator.generateApiTokenWithLength(32);
        System.out.println("定长API Token: " + token3);
    }
} 