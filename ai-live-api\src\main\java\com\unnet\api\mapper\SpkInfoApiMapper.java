package com.unnet.api.mapper;

import com.unnet.api.entity.VoiceData;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SpkInfoApiMapper {

    List<VoiceData> findById(String userId);

    void insert(VoiceData voiceData);

    void update(VoiceData voiceData);

    void delete(String spkName,String userId);

    VoiceData find(String userId,String spkName);

    int exist(String userId,String spkName);
}
