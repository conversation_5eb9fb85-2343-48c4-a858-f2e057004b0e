---
description: 
globs: 
alwaysApply: false
---
# API安全机制

## 认证体系

### API Key认证
- 通过 `X-API-KEY` 请求头传递
- 在配置文件中管理有效的API Key
- 过滤器: [ApiKeyAuthFilter.java](mdc:src/main/java/com/unnet/api/filter/ApiKeyAuthFilter.java)

### 防重放攻击
项目实现了完整的防重放攻击机制：

#### 必需的请求头
1. **X-Request-Nonce**: 请求唯一标识符
   - 格式：UUID (如 `123e4567-e89b-12d3-a456-************`)
   - 每个请求必须使用不同的Nonce
   - 服务器会缓存已处理的Nonce

2. **X-Request-Timestamp**: Unix时间戳
   - 必须在服务器当前时间的±5分钟范围内
   - 格式：从1970年1月1日至今的秒数

#### 实现组件
- 过滤器: [ReplayAttackFilter.java](mdc:src/main/java/com/unnet/api/filter/ReplayAttackFilter.java)
- 配置: [AppProperties.java](mdc:src/main/java/com/unnet/api/config/AppProperties.java)

## 配置示例

```yaml
app:
  api:
    keys:
      - key: "api-key-client1"
        clientId: "client1"
      - key: "api-key-client2"  
        clientId: "client2"
  replay-attack:
    timestamp-tolerance-seconds: 300  # 5分钟容差
    nonce-cache-minutes: 10          # Nonce缓存10分钟
```

## 请求示例

```bash
curl -X GET \
  http://localhost:8081/api/demo/hello \
  -H 'X-API-KEY: api-key-client1' \
  -H 'X-Request-Nonce: 123e4567-e89b-12d3-a456-************' \
  -H 'X-Request-Timestamp: 1633933200'
```

## 安全最佳实践

1. **API Key管理**: 定期轮换API Key
2. **Nonce生成**: 使用强随机性的UUID
3. **时间戳验证**: 客户端时间同步很重要
4. **HTTPS**: 生产环境必须使用HTTPS
5. **日志记录**: 记录所有认证失败的请求
