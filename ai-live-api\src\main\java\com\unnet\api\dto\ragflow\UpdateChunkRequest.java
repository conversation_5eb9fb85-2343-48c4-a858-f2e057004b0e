package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新已存在分段的请求体。所有字段均为可选，仅提供需要修改的字段即可。")
public class UpdateChunkRequest {

    @Schema(description = "新的分段文本内容 (可选)")
    private String content;

//    @Schema(description = "新的与分段相关的关键词或短语列表 (可选)")
//    private List<String> important_keywords;
//
//    @Schema(description = "分段的可用状态 (可选)。true: 可用 (默认), false: 不可用")
//    private Boolean available;
} 