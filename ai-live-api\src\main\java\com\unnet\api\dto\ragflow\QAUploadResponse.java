package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA上传响应")
public class QAUploadResponse {

    @Schema(description = "QA对ID", example = "6000d023cc82a48a")
    private String id;

    @Schema(description = "问题", example = "比亚迪秦2023款的售价是多少？")
    private String question;

    @Schema(description = "答案", example = "大概10.9万")
    private String answer;
} 