<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unnet.api.mapper.SpkInfoApiMapper">


    <select id="findById" resultType="com.unnet.api.entity.VoiceData">
        select * from voice where user_id = #{userId}
    </select>

    <select id="find" resultType="com.unnet.api.entity.VoiceData">
        select * from voice where user_id = #{userId} and  spk_name = #{spkName}
    </select>

    <select id="exist" resultType="int">
        select count(*) from voice where user_id = #{userId} and  spk_name = #{spkName}
    </select>

    <insert id="insert" parameterType="com.unnet.api.entity.VoiceData">
        insert into voice(spk_name,spk_gender,spk_desc,prompt_text,user_id,file_path,test_path)
        values(#{spkName},#{spkGender},#{spkDesc},#{promptText},#{userId},#{filePath},#{testPath})
    </insert>

    <insert id="update" parameterType="com.unnet.api.entity.VoiceData">
        update voice set spk_gender=#{spkGender},spk_desc=#{spkDesc},prompt_text=#{promptText},file_path=#{filePath},test_path=#{testPath}
        where spk_name=#{spkName} and user_id=#{userId}
    </insert>

    <delete id="delete">
        delete from voice where spk_name = #{spkName} and user_id = #{userId}
    </delete>

</mapper>
