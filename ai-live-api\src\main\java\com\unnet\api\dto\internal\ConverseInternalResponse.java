package com.unnet.api.dto.internal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ConverseInternalResponse {
    private int code;
    private DataDetails data;

    @lombok.Data
    public static class DataDetails {
        private String answer;

        @JsonProperty("audio_binary")
        private Object audioBinary;

        @JsonProperty("created_at")
        private Object createdAt;

        private String id;

        private String prompt;

        private Object reference;

        @JsonProperty("session_id")
        private String sessionId;
    }
} 