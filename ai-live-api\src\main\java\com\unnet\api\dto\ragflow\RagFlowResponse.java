package com.unnet.api.dto.ragflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * RagFlow API响应封装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RagFlowResponse<T> {
    
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功响应
     */
    public static <T> RagFlowResponse<T> success(T data) {
        return new RagFlowResponse<>(0, "success", data);
    }
    
    /**
     * 失败响应
     */
    public static <T> RagFlowResponse<T> error(Integer code, String message) {
        return new RagFlowResponse<>(code, message, null);
    }
} 