---
description: 
globs: 
alwaysApply: false
---
# 开发指南

## 编码规范

### Java编码标准
- 使用Java 8+特性
- 遵循阿里巴巴Java开发手册
- 使用Lombok减少样板代码
- 所有public方法必须有Javadoc注释

### Spring Boot最佳实践

#### 1. 控制器设计
```java
@RestController
@RequestMapping("/api/v1/example")
@Api(tags = "示例API")
@Validated
public class ExampleController {
    
    @PostMapping
    @ApiOperation("创建示例")
    public ApiResponse<ExampleResponse> create(@Valid @RequestBody ExampleRequest request) {
        // 实现逻辑
    }
}
```

#### 2. 服务层设计
- 接口与实现分离
- 使用 `@Service` 注解
- 业务异常使用 `BusinessException`
- 关键操作添加 `@Transactional`

#### 3. DTO设计
- 使用 `@Valid` 进行参数校验
- 添加适当的校验注解 (`@NotNull`, `@NotBlank`, `@Email` 等)
- 使用 `ApiResponse<T>` 包装响应

#### 4. 异常处理
- 统一使用 [GlobalExceptionHandler.java](mdc:src/main/java/com/unnet/api/exception/GlobalExceptionHandler.java)
- 业务异常继承 `BusinessException`
- 返回统一格式的错误响应

## 开发流程

### 1. 添加新API
1. 在 `dto/` 包中创建Request/Response DTO
2. 在 `controller/` 包中创建Controller
3. 在 `service/` 包中创建Service接口和实现
4. 添加必要的校验和异常处理
5. 编写单元测试

### 2. 配置管理
- 使用 `@ConfigurationProperties` 管理配置
- 区分开发、测试、生产环境配置
- 敏感信息不要硬编码

### 3. 日志记录
- 使用SLF4J API
- 关键业务操作添加日志
- 异常处理时记录详细错误信息
- 使用 [LoggingAspect.java](mdc:src/main/java/com/unnet/api/aspect/LoggingAspect.java) 进行方法级日志

### 4. 缓存使用
- 使用Spring Cache注解 (`@Cacheable`, `@CacheEvict`)
- 配置参考 [CacheConfig.java](mdc:src/main/java/com/unnet/api/config/CacheConfig.java)
- 合理设置缓存过期时间

## 测试指南

### 单元测试
- 所有Service方法必须有单元测试
- 使用 `@MockBean` 模拟依赖
- 测试覆盖率目标：80%+

### 集成测试
- 使用 `@SpringBootTest` 进行集成测试
- 测试API认证和参数校验
- 测试异常处理逻辑

## 部署和运维

### 构建命令
```bash
# 清理构建
./mvn clean package -DskipTests

# 运行应用
./mvn spring-boot:run

# 指定环境运行
./mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 健康检查
- 使用Spring Boot Actuator
- 访问 `/actuator/health` 检查应用状态
- 监控关键指标和日志
