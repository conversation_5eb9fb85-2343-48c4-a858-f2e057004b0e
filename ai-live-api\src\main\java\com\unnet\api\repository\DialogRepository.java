package com.unnet.api.repository;

import com.unnet.api.entity.DialogEntity;
import com.unnet.api.entity.DocumentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DialogRepository extends JpaRepository<DialogEntity, String> {

    /**
     * 根据数据集ID查询对话实体
     * @param datasetId 数据集ID
     * @return 对话实体的Optional包装
     */
    Optional<DialogEntity> findByKbIds(String datasetId);

    /**
     * 根据数据集ID查询对话实体
     * @param datasetId 数据集ID
     * @param status 状态值，1-有效，0-无效
     * @return 对话实体的Optional包装
     */
    Optional<DialogEntity> findByKbIdsAndStatus(String datasetId, String status);
    
    /**
     * 根据数据集ID查询对话实体（如果kbIds字段包含该datasetId）
     * @param datasetId 数据集ID
     * @return 对话实体的Optional包装
     */
    Optional<DialogEntity> findByKbIdsContaining(String datasetId);
    
    /**
     * 查找只包含指定知识库ID的聊天助手
     * 这里假设kbIds字段存储的是JSON数组格式，如：["kb1","kb2"]
     * 我们需要找到kbIds只包含一个指定知识库ID的记录
     * @param datasetId 知识库ID
     * @return 匹配的聊天助手列表
     */
    @Query("SELECT d FROM DialogEntity d WHERE d.kbIds = CONCAT('[\"', :datasetId, '\"]')")
    List<DialogEntity> findByExactKbId(@Param("datasetId") String datasetId);

    // 更新方法：更新 dialog 表中的 prompt_config 字段
    @Modifying
    @Query(value = "UPDATE dialog SET prompt_config = :promptConfig WHERE id = :id", nativeQuery = true)
    void updateDialog(@Param("id") String id, @Param("promptConfig") String promptConfig);

    // 查询方法：查看聊天助手的设置（通过 id 获取 prompt_config）
    @Query(value = "SELECT prompt_config FROM dialog WHERE id = :id", nativeQuery = true)
    String getDialog(@Param("id") String id);
} 