package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA对查询结果列表")
public class QAQueryListResponse {

    @Schema(description = "所有知识库的QA对数据")
    private List<QAQueryResponse> data;

    @Schema(description = "总的知识库数量", example = "2")
    private Integer total_datasets;

    @Schema(description = "总的QA对数量", example = "25")
    private Integer total_qa_pairs;
} 