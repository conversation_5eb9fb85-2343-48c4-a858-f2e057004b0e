package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "过滤后的分段列表响应数据")
public class FilteredChunkListResponse {

    @Schema(description = "分段列表")
    private List<FilteredChunkResponse> chunks;

    @Schema(description = "总数", example = "2")
    private Integer total;
} 