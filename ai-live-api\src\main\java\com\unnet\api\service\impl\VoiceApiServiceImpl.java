package com.unnet.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.api.config.RouterConfig;
import com.unnet.api.service.VoiceApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Date;
import java.util.List;


@Service
@Slf4j
public class VoiceApiServiceImpl implements VoiceApiService {

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RouterConfig routerConfig;
    //生成音频
    @Override
    public JSONObject generateAudio(List<Object> data) throws Exception {
        //获取合适路由
        String API_URL = null;
        if(data.size() == 11) {
            String testIp = (String) data.get(10);
            log.info("-----进入测试代码，指定TTS节点IP：{}", testIp);
            if(testIp.startsWith("10.") || testIp.startsWith("221.")) {
                API_URL = "http://"+testIp;
            }
        } else {
            API_URL = routerConfig.findIp();
        }

        JSONObject jsonResponse = null;
        try {
            // 将请求参数包装在 "data" 字段中
            JSONObject requestBody = new JSONObject();
            requestBody.put("data", data);

            // 发送 POST 请求
            HttpURLConnection connection = (HttpURLConnection) new URL(API_URL + "/gradio_api/call/generate_audio").openConnection();
            System.out.println("Request URL: " + API_URL + "/gradio_api/call/generate_audio");
            System.out.println("Request Body: " + requestBody.toString());

            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);

            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 获取响应
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }

                    // 解析 JSON 响应
                    jsonResponse = JSON.parseObject(response.toString());
                    System.out.println("Response: " + jsonResponse.toJSONString());

                    routerConfig.SetIpEvent(jsonResponse.get("event_id").toString(), API_URL);

                    getStatus(jsonResponse.get("event_id").toString());
                }
            } else {
                // 打印错误信息
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String errorLine;
                    while ((errorLine = br.readLine()) != null) {
                        errorResponse.append(errorLine.trim());
                    }
                    System.err.println("Error Response: " + errorResponse.toString());
                }
                return null;
            }
        } catch ( Exception e) {

        } finally {
            return jsonResponse;
        }
    }

    //音频流式接口
    @Override
    public String getAudio(String eventId) throws Exception {
        String API_URL = routerConfig.getIpEvent(eventId);
        String url = API_URL + "/gradio_api/call/generate_audio/" + eventId;
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Accept", "text/event-stream");
        int responseCode = connection.getResponseCode();
        System.out.println("---------------------0.000-----------------------------------");
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                String event = null;
                String data = null;
                String audioUrl = null;
                long startDate = new Date().getTime();
                while ((line = br.readLine()) != null) {
                    if (line.startsWith("event:")) {
                        event = line.substring("event:".length()).trim();
                    } else if (line.startsWith("data:")) {
                        data = line.substring("data:".length()).trim();
                    }
                    if (event != null && data != null) {
                        System.out.println("event: " + event);
                        System.out.println("data: " + data);
                        long times = new Date().getTime()-startDate;
                        //记录流式数据返回时间
                        System.out.println("---------------------"+(times/1000.0)+"-----------------------------------");
                        JSONArray jsonArray = JSON.parseArray(data);
                        if ("complete".equals(event)) {
                            if (jsonArray != null && !jsonArray.isEmpty()) {
                                JSONObject audioInfo = jsonArray.getJSONObject(0);
                                audioUrl = audioInfo.getString("path");
                                // 拼接最终文件下载实际路径
                                audioUrl = API_URL +"/gradio_api/stream/"+audioUrl.replace("/playlist.m3u8", "/playlist-file");
                                System.out.println("Audio URL: " + audioUrl);
                            }
                            return audioUrl;
                        } else if ("error".equals(event)) {
                            System.out.println("生成过程中发生错误: " + data);
                            return null;
                        }
                        // 重置 event 和 data
                        event = null;
                        data = null;
                    }
                }
            }
        } else {
            System.out.println("GET Error Response Code: " + responseCode);
            return null;
        }
        return null;
    }

    @Override
    public ResponseEntity<byte[]>  getM3U8(String event_id, String path) throws Exception {
        // 1. 构建目标m3u8文件URL
        String m3u8Url = routerConfig.getIpEvent(event_id) + "/gradio_api/stream/" + path + "/playlist.m3u8";
        if (m3u8Url == null || m3u8Url.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        // 2. 使用HTTP客户端下载资源
        try {
            RestTemplate restTemplate = new RestTemplate();

            // 设置超时（避免长时间阻塞）
            restTemplate.setRequestFactory(new SimpleClientHttpRequestFactory() {
                {
                    setConnectTimeout(5000);  // 5秒连接超时
                    setReadTimeout(10000);    // 10秒读取超时
                }
            });

            // 3. 执行HTTP请求
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    m3u8Url,
                    HttpMethod.GET,
                    null,
                    byte[].class
            );

            // 4. 处理响应
            if (response.getStatusCode().is2xxSuccessful() && response.hasBody()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType("application/vnd.apple.mpegurl")) // M3U8标准MIME类型
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                "inline; filename=playlist.m3u8") // 浏览器内联播放
                        .body(response.getBody());
            } else {
                return ResponseEntity.status(response.getStatusCode()).build();
            }

        } catch (RestClientException e) {
            log.error("下载m3u8文件失败: {}", m3u8Url, e);
            return ResponseEntity.status(HttpStatus.BAD_GATEWAY).build();
        }
    }

    @Override
    public ResponseEntity<byte[]>  getPartAudio(String event_id, String path, String uuid_aac) throws Exception {
        // 获取音频文件的 URL
        String audioUrl = routerConfig.getIpEvent(event_id) + "/gradio_api/stream/"+path+"/"+uuid_aac;
        if (audioUrl == null || audioUrl.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        // 2. 使用HTTP客户端下载资源
        try {
            // 使用Spring的RestTemplate（同步）
            RestTemplate restTemplate = new RestTemplate();

            // 设置超时时间（重要！）
            restTemplate.setRequestFactory(new SimpleClientHttpRequestFactory() {
                {
                    setConnectTimeout(5000);  // 5秒连接超时
                    setReadTimeout(30000);    // 30秒读取超时
                }
            });

            // 3. 执行HTTP请求
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    audioUrl,
                    HttpMethod.GET,
                    null,
                    byte[].class
            );

            // 4. 处理响应
            if (response.getStatusCode().is2xxSuccessful() && response.hasBody()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType("audio/wav"))
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                "attachment; filename=audio_" + System.currentTimeMillis() + ".wav")
                        .body(response.getBody());
            } else {
                return ResponseEntity.status(response.getStatusCode()).build();
            }

        } catch (RestClientException e) {
            log.error("下载音频资源失败: {}", audioUrl, e);
            return ResponseEntity.status(HttpStatus.BAD_GATEWAY).build();
        }
    }

    @Override
    public ResponseEntity<byte[]>  getFullAudio(String event_id, String path) throws Exception {
        // 1. 构建目标音频URL
        String audioUrl = routerConfig.getIpEvent(event_id) + "/gradio_api/stream/" + path + "/playlist-file";
        if (audioUrl == null || audioUrl.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        // 2. 使用HTTP客户端下载资源
        try {
            // 使用Spring的RestTemplate（同步）
            RestTemplate restTemplate = new RestTemplate();

            // 设置超时时间（重要！）
            restTemplate.setRequestFactory(new SimpleClientHttpRequestFactory() {
                {
                    setConnectTimeout(5000);  // 5秒连接超时
                    setReadTimeout(30000);    // 30秒读取超时
                }
            });

            // 3. 执行HTTP请求
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    audioUrl,
                    HttpMethod.GET,
                    null,
                    byte[].class
            );

            // 4. 处理响应
            if (response.getStatusCode().is2xxSuccessful() && response.hasBody()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType("audio/wav"))
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                "attachment; filename=audio_" + System.currentTimeMillis() + ".wav")
                        .body(response.getBody());
            } else {
                return ResponseEntity.status(response.getStatusCode()).build();
            }

        } catch (RestClientException e) {
            log.error("下载音频资源失败: {}", audioUrl, e);
            return ResponseEntity.status(HttpStatus.BAD_GATEWAY).build();
        }
    }

    //异步查询流式接口
    @Async("streamExecutor")
    public void getStatus(String eventId) throws Exception {
        routerConfig.getStatus(eventId);
    }

}
