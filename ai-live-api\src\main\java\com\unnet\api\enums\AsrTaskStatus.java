package com.unnet.api.enums;

/**
 * ASR任务状态枚举
 */
public enum AsrTaskStatus {
    
    /**
     * 等待处理
     */
    PENDING("pending"),
    
    /**
     * 处理中
     */
    PROCESSING("processing"),
    
    /**
     * 完成
     */
    COMPLETED("completed"),
    
    /**
     * 失败
     */
    FAILED("failed");
    
    private final String value;
    
    AsrTaskStatus(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    public static AsrTaskStatus fromValue(String value) {
        for (AsrTaskStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的任务状态: " + value);
    }
} 