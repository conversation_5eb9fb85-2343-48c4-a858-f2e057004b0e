package com.unnet.api.dto.ragflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 过滤后的知识库响应数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilteredDatasetResponse {
    
    /**
     * 创建日期
     */
    private String create_date;
    
    /**
     * 创建时间戳
     */
    private Long create_time;
    
    /**
     * 创建者ID
     */
    private String created_by;
    
    /**
     * 知识库描述
     */
    private String description;
    
    /**
     * 知识库ID
     */
    private String id;
    
    /**
     * 知识库名称
     */
    private String name;
    
    /**
     * 更新日期
     */
    private String update_date;
    
    /**
     * 更新时间戳
     */
    private Long update_time;
} 