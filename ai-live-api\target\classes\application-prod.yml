spring:
  datasource:
    url: ********************************************************************************************************
    username: root
    password: infini_rag_flow
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 100      # 最大连接数，根据实际并发调整
      minimum-idle: 20           # 最小空闲连接数
      idle-timeout: 300000       # 空闲连接存活时间（毫秒），5分钟，适当延长空闲时间
      connection-timeout: 30000  # 获取连接超时时间（毫秒）
      max-lifetime: 1200000      # 连接最大存活时间（毫秒）
  jpa:
    hibernate:
      # 生产环境使用validate，只验证表结构不自动建表，更安全
      # 如需初始化表结构，请手动执行SQL脚本或临时改为update后改回
      ddl-auto: none
    show-sql: false  # 生产环境关闭SQL日志
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
  elasticsearch:
    uris: http://localhost:1200
    username: elastic
    password: infini_rag_flow
logging:
  level:
    com.unnet.api.service.impl.ApiLogServiceImpl: OFF


redis:
  # 地址
  host: localhost
  port: 30379
  database: 0
  # 密码
  password: password4redis

# 应用自定义配置
app:
  # API密钥配置
  api:
    keys:
      - key: "api-u8gTb0vIFGyJ0H8Gzev9zoLdePFdSBka"
        clientId: "haiyun-ai-live"
  security:
    # 防重放攻击配置
    replay-protection:
      enabled: false  # 是否启用防重放攻击保护
      timestamp-tolerance-seconds: 300  # 时间戳容忍度（秒）
      nonce-cache-minutes: 10  # Nonce缓存时间（分钟）

# 生产环境配置
server:
  port: 8081

# 生产环境禁用Swagger
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

# RagFlow API配置
ragflow:
  api:
    baseUrl: http://***********  # 生产环境RagFlow API基础URL，请根据实际情况修改
    key: ragflow-hjMjZkZDBlMzdiMDExZjBiZjg0MDI0Mm  # 生产环境RagFlow API密钥，请替换为实际的API Key

# LLM配置（自定义配置）
llm:
  base-url: http://***********:9997/v1
  model: qwen3    # 低参数模型：qwen2.5-instruct
  timeout: 600

# ASR语音识别服务配置
asr:
  service:
    url: http://localhost:5077