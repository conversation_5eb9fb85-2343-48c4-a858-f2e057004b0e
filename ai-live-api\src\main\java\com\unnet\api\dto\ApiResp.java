package com.unnet.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用API响应封装
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResp<T> {
    
    /**
     * 业务状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功响应
     */
    public static <T> ApiResp<T> success(T data) {
        return new ApiResp<>(0, "操作成功", data);
    }
    
    /**
     * 成功响应（带消息）
     */
    public static <T> ApiResp<T> success(String message, T data) {
        return new ApiResp<>(0, message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResp<T> error(Integer code, String message) {
        return new ApiResp<>(code, message, null);
    }
} 