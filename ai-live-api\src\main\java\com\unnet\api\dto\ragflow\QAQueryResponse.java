package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA对查询响应")
public class QAQueryResponse {

    @Schema(description = "知识库ID", example = "f6a4d3023aa811f08c930242ac110006")
    private String dataset_id;

    @Schema(description = "QA文档ID", example = "doc_abc123")
    private String qa_document_id;

    @Schema(description = "该知识库下的QA对列表")
    private List<FilteredChunkResponse> qa_pairs;

    @Schema(description = "该知识库下的QA对总数", example = "10")
    private Integer total_count;
} 