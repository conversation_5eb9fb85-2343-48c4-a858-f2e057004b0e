package com.unnet.api.service.impl;

import com.unnet.api.entity.ApiLog;
import com.unnet.api.repository.ApiLogRepository;
import com.unnet.api.service.ApiLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.Future;

@Slf4j
@Service
@RequiredArgsConstructor
public class ApiLogServiceImpl implements ApiLogService {

    private final ApiLogRepository apiLogRepository;

    @Async("apiLogExecutor")
    @Override
    public Future<?> saveApiLog(ApiLog apiLog) {
        try {
            apiLogRepository.save(apiLog);
        } catch (Exception e) {
            log.error("保存API日志失败", e);
        }
        return null;
    }
} 