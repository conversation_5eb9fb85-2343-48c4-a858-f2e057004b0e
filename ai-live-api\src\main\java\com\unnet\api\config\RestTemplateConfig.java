package com.unnet.api.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 */
@Configuration
public class RestTemplateConfig {

    @Value("${llm.timeout:120}")
    private int timeout;

    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        
        // 设置连接超时时间（毫秒）- 连接建立的超时时间
        factory.setConnectTimeout(30 * 1000); // 30秒连接超时
        
        // 设置读取超时时间（毫秒）- 等待响应数据的超时时间，这是最重要的配置
        factory.setReadTimeout(timeout * 1000);
        
        // 设置连接请求超时时间（毫秒）- 从连接池获取连接的超时时间
        factory.setConnectionRequestTimeout(30 * 1000); // 30秒

        return new RestTemplate(factory);
    }
} 