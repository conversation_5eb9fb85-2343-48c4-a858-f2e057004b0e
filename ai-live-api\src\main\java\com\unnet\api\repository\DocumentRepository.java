package com.unnet.api.repository;

import com.unnet.api.entity.DocumentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentRepository extends JpaRepository<DocumentEntity, String> {

    Optional<DocumentEntity> findByKbIdAndId(String kbId, String id);

    /**
     * 根据知识库ID和解析器ID查找文档
     * @param kbId 知识库ID
     * @param parserId 解析器ID（如"qa"）
     * @return 匹配的文档列表
     */
    List<DocumentEntity> findByKbIdAndParserId(String kbId, String parserId);

    // 如果需要，可以添加其他查询方法，例如根据kbId查找所有文档等
    // List<DocumentEntity> findByKbId(String kbId);

    @Query(value = "SELECT id FROM document WHERE kb_id = :datasetId AND parser_id = 'qa' AND chunk_num > 0 AND status = 1", nativeQuery = true)
    List<String> getDocumentIdByDatasetId(@Param("datasetId") String datasetId);

} 