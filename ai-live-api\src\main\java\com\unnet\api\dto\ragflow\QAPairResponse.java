package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "QA对响应")
public class QAPairResponse {

    @Schema(description = "QA对ID", example = "6000d023cc82a48a")
    private String id;

    @Schema(description = "知识库ID", example = "f6a4d3023aa811f08c930242ac110006")
    private String dataset_id;

    @Schema(description = "问题", example = "比亚迪秦2023款的售价是多少？")
    private String question;

    @Schema(description = "答案", example = "大概10.9万")
    private String answer;

    @Schema(description = "是否可用", example = "true")
    private Boolean available;
} 