package com.unnet.api.dto.ragflow;

import lombok.Data;

/**
 * 过滤后的文档响应数据
 */
@Data
public class FilteredDocumentResponse {
    private String create_date;
    private Long create_time;
    private String dataset_id;
    private Integer chunk_count;
    private String chunk_method;
    private String id;
    private String name;
    private Long size;
    private String run;
    private String status;
    private String type;
    private String update_date;
    private Long update_time;
} 