package com.unnet.api.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.unnet.api.dto.ApiResp;
import com.unnet.api.dto.ragflow.DatasetRequest;
import com.unnet.api.dto.ragflow.FilteredDatasetResponse;
import com.unnet.api.dto.ragflow.FilteredDocumentResponse;
import com.unnet.api.dto.ragflow.FilteredDocumentListResponse;
import com.unnet.api.dto.ragflow.FilteredChunkResponse;
import com.unnet.api.dto.ragflow.FilteredChunkListResponse;
import com.unnet.api.dto.ragflow.RagFlowResponse;
import com.unnet.api.dto.ragflow.DocumentDownloadResult;
import com.unnet.api.dto.ragflow.UpdateDocumentRequest;
import com.unnet.api.dto.ragflow.AddChunkToDocumentRequest;
import com.unnet.api.dto.ragflow.DeleteChunksRequest;
import com.unnet.api.dto.ragflow.UpdateChunkRequest;
import com.unnet.api.dto.ragflow.ChatAssistantRequest;
import com.unnet.api.dto.ragflow.QAUploadRequest;
import com.unnet.api.dto.ragflow.ParseDocumentsRequest;
import com.unnet.api.dto.ragflow.QAQueryResponse;
import com.unnet.api.dto.ragflow.QAQueryListResponse;
import com.unnet.api.dto.ragflow.QAPairResponse;
import com.unnet.api.dto.ragflow.QAPairListResponse;
import com.unnet.api.dto.ragflow.QAUploadResponse;
import com.unnet.api.service.RagBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Arrays;
import java.util.ArrayList;


import com.unnet.api.entity.DocumentEntity;
import com.unnet.api.entity.KnowledgebaseEntity;
import com.unnet.api.entity.DialogEntity;
import com.unnet.api.repository.DocumentRepository;
import com.unnet.api.repository.KnowledgebaseRepository;
import com.unnet.api.repository.DialogRepository;
import org.springframework.transaction.annotation.Transactional;

/**
 * RagFlow API服务实现
 */
@Service
@Slf4j
@EnableAsync
public class RagBaseServiceImpl implements RagBaseService {

    @Value("${ragflow.api.baseUrl}")
    private String baseUrl;
    
    @Value("${ragflow.api.key}")
    private String apiKey;


    private final RestTemplate restTemplate;
    private final DocumentRepository documentRepository;
    private final KnowledgebaseRepository knowledgebaseRepository;
    private final DialogRepository dialogRepository;
    
    public RagBaseServiceImpl(RestTemplate restTemplate, DocumentRepository documentRepository, KnowledgebaseRepository knowledgebaseRepository, DialogRepository dialogRepository) {
        this.restTemplate = restTemplate;
        this.documentRepository = documentRepository;
        this.knowledgebaseRepository = knowledgebaseRepository;
        this.dialogRepository = dialogRepository;
    }
    
    /**
     * 获取请求头
     */
    private HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);
        return headers;
    }
    
    /**
     * 过滤数据集响应数据，只保留指定字段
     */
    private RagFlowResponse<?> filterDatasetResponse(RagFlowResponse<?> originalResponse) {
        return filterResponse(originalResponse, "dataset");
    }
    
    /**
     * 过滤文档响应数据，只保留指定字段
     */
    private RagFlowResponse<?> filterDocumentResponse(RagFlowResponse<?> originalResponse) {
        return filterResponse(originalResponse, "document");
    }
    
    /**
     * 过滤分段响应数据，只保留指定字段
     */
    private RagFlowResponse<?> filterChunkResponse(RagFlowResponse<?> originalResponse) {
        return filterResponse(originalResponse, "chunk");
    }
    
    /**
     * 通用响应过滤器，根据类型过滤不同的响应数据
     * @param originalResponse 原始响应
     * @param filterType 过滤类型："dataset" 或 "document" 或 "chunk"
     */
    private RagFlowResponse<?> filterResponse(RagFlowResponse<?> originalResponse, String filterType) {
        if (originalResponse == null || originalResponse.getData() == null) {
            return new RagFlowResponse<>(originalResponse != null ? originalResponse.getCode() : null, 
                                       originalResponse != null ? originalResponse.getMessage() : null, null);
        }
        
        Object data = originalResponse.getData();
        
        if ("dataset".equals(filterType)) {
            return filterDatasetData(originalResponse, data);
        } else if ("document".equals(filterType)) {
            return filterDocumentData(originalResponse, data);
        } else if ("chunk".equals(filterType)) {
            return filterChunkData(originalResponse, data);
        }
        
        // 如果类型不匹配，返回原始响应
        return originalResponse;
    }
    
    /**
     * 过滤数据集数据
     */
    private RagFlowResponse<?> filterDatasetData(RagFlowResponse<?> originalResponse, Object data) {
        // 处理单个对象的情况（创建、更新接口）
        if (data instanceof Map) {
            Map<String, Object> dataMap = (Map<String, Object>) data;
            FilteredDatasetResponse filteredData = createFilteredDataset(dataMap);
            return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredData);
        }
        
        // 处理数组的情况（查询列表接口）
        if (data instanceof List) {
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) data;
            List<FilteredDatasetResponse> filteredList = new ArrayList<>();
            
            for (Map<String, Object> dataMap : dataList) {
                filteredList.add(createFilteredDataset(dataMap));
            }
            
            return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredList);
        }
        
        return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), null);
    }
    
    /**
     * 过滤文档数据
     */
    private RagFlowResponse<?> filterDocumentData(RagFlowResponse<?> originalResponse, Object data) {
        // 处理直接是List的情况（某些接口直接返回文档数组）
        if (data instanceof List) {
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) data;
            List<FilteredDocumentResponse> filteredList = new ArrayList<>();
            
            for (Map<String, Object> dataMap : dataList) {
                filteredList.add(createFilteredDocument(dataMap));
            }
            
            return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredList);
        }
        
        if (data instanceof Map) {
            Map<String, Object> dataMap = (Map<String, Object>) data;
            
            // 检查是否是文档列表结构 (包含docs和total字段)
            if (dataMap.containsKey("docs") && dataMap.containsKey("total")) {
                Object docsData = dataMap.get("docs");
                Object totalData = dataMap.get("total");
                
                if (docsData instanceof List) {
                    List<Map<String, Object>> docsList = (List<Map<String, Object>>) docsData;
                    List<FilteredDocumentResponse> filteredDocs = new ArrayList<>();
                    
                    for (Map<String, Object> docMap : docsList) {
                        filteredDocs.add(createFilteredDocument(docMap));
                    }
                    
                    Integer total = totalData instanceof Number ? ((Number) totalData).intValue() : 0;
                    FilteredDocumentListResponse filteredData = new FilteredDocumentListResponse(filteredDocs, total);
                    
                    return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredData);
                }
            } else {
                // 处理单个文档对象
                FilteredDocumentResponse filteredData = createFilteredDocument(dataMap);
                return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredData);
            }
        }
        
        return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), null);
    }
    
    /**
     * 过滤分段数据
     */
    private RagFlowResponse<?> filterChunkData(RagFlowResponse<?> originalResponse, Object data) {
        if (data instanceof Map) {
            Map<String, Object> dataMap = (Map<String, Object>) data;
            
            // 检查是否是分段列表结构 (包含chunks和total字段)
            if (dataMap.containsKey("chunks") && dataMap.containsKey("total")) {
                Object chunksData = dataMap.get("chunks");
                Object totalData = dataMap.get("total");
                
                if (chunksData instanceof List) {
                    List<Map<String, Object>> chunksList = (List<Map<String, Object>>) chunksData;
                    List<FilteredChunkResponse> filteredChunks = new ArrayList<>();
                    
                    for (Map<String, Object> chunkMap : chunksList) {
                        filteredChunks.add(createFilteredChunk(chunkMap));
                    }
                    
                    Integer total = totalData instanceof Number ? ((Number) totalData).intValue() : 0;
                    FilteredChunkListResponse filteredData = new FilteredChunkListResponse(filteredChunks, total);
                    
                    return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredData);
                }
            } else {
                // 处理单个分段对象
                FilteredChunkResponse filteredData = createFilteredChunk(dataMap);
                return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredData);
            }
        }
        
        // 处理直接是List的情况（某些接口直接返回分段数组）
        if (data instanceof List) {
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) data;
            List<FilteredChunkResponse> filteredList = new ArrayList<>();
            
            for (Map<String, Object> dataMap : dataList) {
                filteredList.add(createFilteredChunk(dataMap));
            }
            
            return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), filteredList);
        }
        
        return new RagFlowResponse<>(originalResponse.getCode(), originalResponse.getMessage(), null);
    }
    
    /**
     * 从Map创建过滤后的数据集对象
     */
    private FilteredDatasetResponse createFilteredDataset(Map<String, Object> dataMap) {
        FilteredDatasetResponse filteredData = new FilteredDatasetResponse();
        
        filteredData.setCreate_date((String) dataMap.get("create_date"));
        filteredData.setCreate_time(dataMap.get("create_time") instanceof Number ? 
            ((Number) dataMap.get("create_time")).longValue() : null);
        filteredData.setCreated_by((String) dataMap.get("created_by"));
        filteredData.setDescription((String) dataMap.get("description"));
        filteredData.setId((String) dataMap.get("id"));
        filteredData.setName((String) dataMap.get("name"));
        filteredData.setUpdate_date((String) dataMap.get("update_date"));
        filteredData.setUpdate_time(dataMap.get("update_time") instanceof Number ? 
            ((Number) dataMap.get("update_time")).longValue() : null);
        
        return filteredData;
    }
    
    /**
     * 从Map创建过滤后的文档对象
     */
    private FilteredDocumentResponse createFilteredDocument(Map<String, Object> dataMap) {
        FilteredDocumentResponse filteredData = new FilteredDocumentResponse();
        
        filteredData.setCreate_date((String) dataMap.get("create_date"));
        filteredData.setCreate_time(dataMap.get("create_time") instanceof Number ? 
            ((Number) dataMap.get("create_time")).longValue() : null);
        filteredData.setChunk_count(dataMap.get("chunk_count") instanceof Number ?
                ((Number) dataMap.get("chunk_count")).intValue() : null);
        filteredData.setChunk_method((String) dataMap.get("chunk_method"));
        filteredData.setDataset_id((String) dataMap.get("dataset_id"));
        filteredData.setId((String) dataMap.get("id"));
        filteredData.setName((String) dataMap.get("name"));
        filteredData.setSize(dataMap.get("size") instanceof Number ? 
            ((Number) dataMap.get("size")).longValue() : null);
        filteredData.setRun((String) dataMap.get("run"));
        filteredData.setStatus((String) dataMap.get("status"));
        filteredData.setType((String) dataMap.get("type"));
        filteredData.setUpdate_date((String) dataMap.get("update_date"));
        filteredData.setUpdate_time(dataMap.get("update_time") instanceof Number ? 
            ((Number) dataMap.get("update_time")).longValue() : null);
        
        return filteredData;
    }
    
    /**
     * 从Map创建过滤后的分段对象
     */
    private FilteredChunkResponse createFilteredChunk(Map<String, Object> dataMap) {
        FilteredChunkResponse filteredData = new FilteredChunkResponse();
        
        filteredData.setAvailable(dataMap.get("available") instanceof Boolean ? 
            (Boolean) dataMap.get("available") : null);
        filteredData.setContent((String) dataMap.get("content"));
        filteredData.setDocnm_kwd((String) dataMap.get("docnm_kwd"));
        filteredData.setId((String) dataMap.get("id"));
        filteredData.setImportant_kwd(dataMap.get("important_kwd"));
        filteredData.setPositions(dataMap.get("positions"));
        
        return filteredData;
    }
    
    @Override
    public RagFlowResponse<?> createDataset(DatasetRequest request) {
        // 在知识库名称后拼接当前时间戳，保证唯一性
        String originalName = request.getName();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nameWithTimestamp = originalName + "_" + timestamp;
        request.setName(nameWithTimestamp);
        
        String url = baseUrl + "/api/v1/datasets";
        HttpEntity<DatasetRequest> entity = new HttpEntity<>(request, getHeaders());
        
        log.info("创建知识库请求: {}", request);
        RagFlowResponse<?> response = restTemplate.postForObject(url, entity, RagFlowResponse.class);

        // 检查并处理实际的错误情况
        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("创建知识库操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1); // 设置为通用错误码
                response.setMessage(dataStr); // 将错误信息设置到 message 字段
                // response.setData(null); // 可选：清空 data 字段
                return response;
            }
        }
        
        // 如果知识库创建成功，自动创建对应的聊天助手
        if (response != null && response.getCode() != null && response.getCode() == 0 && response.getData() != null) {
            try {
                // 从响应中提取知识库ID
                String datasetId = extractDatasetIdFromResponse(response);
                if (datasetId != null) {
                    log.info("知识库创建成功，开始创建对应的聊天助手，知识库ID: {}", datasetId);
                    createChatAssistantForDataset(nameWithTimestamp, datasetId); // 传递带时间戳的名称
                } else {
                    log.warn("无法从知识库创建响应中提取知识库ID，跳过聊天助手创建");
                }
            } catch (Exception e) {
                log.error("创建聊天助手失败，但知识库创建成功: {}", e.getMessage(), e);
                // 不影响知识库创建的成功响应
            }
        }
        
        // 过滤响应数据，只返回指定字段
        return filterDatasetResponse(response);
    }
    
    @Override
    @Transactional
    public RagFlowResponse<?> deleteDatasets(List<String> ids) {
        // 添加关键参数验证，防止意外删除所有知识库
        if (ids == null || ids.isEmpty()) {
            log.error("删除知识库失败：知识库ID列表为空");
            // 创建错误响应而不是发送请求
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("知识库ID列表不能为空");
            errorResponse.setData(null);
            return errorResponse;
        }
        
        // 1. 先查找并删除对应的聊天助手
        List<String> chatAssistantIdsToDelete = new ArrayList<>();
        for (String datasetId : ids) {
            try {
                // 查找只包含这个知识库ID的聊天助手
                List<DialogEntity> relatedChatAssistants = dialogRepository.findByExactKbId(datasetId);
                for (DialogEntity dialog : relatedChatAssistants) {
                    chatAssistantIdsToDelete.add(dialog.getId());
                }
                log.info("知识库 {} 关联的聊天助手: {}", datasetId, relatedChatAssistants.size());
            } catch (Exception e) {
                log.warn("查找知识库 {} 关联的聊天助手失败: {}", datasetId, e.getMessage());
            }
        }
        
        // 2. 删除找到的聊天助手
        if (!chatAssistantIdsToDelete.isEmpty()) {
            try {
                log.info("开始删除关联的聊天助手，IDs: {}", chatAssistantIdsToDelete);
                RagFlowResponse<?> deleteAssistantResponse = deleteChatAssistants(chatAssistantIdsToDelete);
                if (deleteAssistantResponse != null && deleteAssistantResponse.getCode() != null && deleteAssistantResponse.getCode() == 0) {
                    log.info("成功删除 {} 个关联的聊天助手", chatAssistantIdsToDelete.size());
                } else {
                    log.warn("删除关联聊天助手失败，但继续删除知识库: {}", deleteAssistantResponse);
                }
            } catch (Exception e) {
                log.error("删除关联聊天助手时发生异常，但继续删除知识库: {}", e.getMessage(), e);
            }
        }
        
        // 3. 删除知识库
        String url = baseUrl + "/api/v1/datasets";
        
        Map<String, List<String>> requestMap = new HashMap<>();
        requestMap.put("ids", ids);
        
        HttpEntity<Map<String, List<String>>> entity = new HttpEntity<>(requestMap, getHeaders());
        
        log.info("删除知识库请求，IDs: {}", ids);
        ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
            url, 
            HttpMethod.DELETE, 
            entity, 
            RagFlowResponse.class
        );
        
        RagFlowResponse<?> response = responseEntity.getBody();
        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("删除知识库操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1);
                response.setMessage(dataStr);
            }
        }
        return response;
    }
    
    @Override
    public RagFlowResponse<?> updateDataset(String id, DatasetRequest request) {
        String url = baseUrl + "/api/v1/datasets/" + id;
        HttpEntity<DatasetRequest> entity = new HttpEntity<>(request, getHeaders());
        
        log.info("更新知识库请求，ID: {}, 请求: {}", id, request);
        ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
            url, 
            HttpMethod.PUT, 
            entity, 
            RagFlowResponse.class
        );
        
        RagFlowResponse<?> response = responseEntity.getBody();
        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("更新知识库操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1);
                response.setMessage(dataStr);
            }
        }
        return response;
    }
    
    @Override
    public RagFlowResponse<?> listDatasets(Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + "/api/v1/datasets");
        
        // 添加查询参数
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                builder.queryParam(entry.getKey(), entry.getValue());
            }
        }
        
        HttpEntity<?> entity = new HttpEntity<>(getHeaders());
        
        log.info("获取知识库列表请求，参数: {}", params);
        ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
            builder.toUriString(), 
            HttpMethod.GET, 
            entity, 
            RagFlowResponse.class
        );
        
        RagFlowResponse<?> response = responseEntity.getBody();
        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("获取知识库列表操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1);
                response.setMessage(dataStr);
                return response;
            }
        }
        System.out.println("response"+response);
        // 过滤响应数据，只返回指定字段
        return filterDatasetResponse(response);
    }

    @Override
    public RagFlowResponse<?> uploadDocument(String datasetId, byte[] file, String fileName) {
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents";
        
        // 创建multipart请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("Authorization", "Bearer " + apiKey);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        ByteArrayResource resource = new ByteArrayResource(file) {
            @Override
            public String getFilename() {
                return fileName;
            }
        };
        
        body.add("file", resource);
        
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);
        
        log.info("上传文档到知识库请求，知识库ID: {}, 文件名: {}", datasetId, fileName);
        RagFlowResponse<?> response = restTemplate.postForObject(url, entity, RagFlowResponse.class);

        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("上传文档操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1);
                response.setMessage(dataStr);
                return response;
            }
        }
        
        // 过滤响应数据，只返回指定字段
        return filterDocumentResponse(response);
    }
    
    @Override
    public RagFlowResponse<?> listDocuments(String datasetId, Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
            baseUrl + "/api/v1/datasets/" + datasetId + "/documents"
        );
        
        // 添加查询参数
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 使用 build(true) 确保不会对已编码的参数进行重复编码
                builder.queryParam(key, value);
            }
        }
        
        // 使用 build() 而不是 build(true)，让Spring自动处理编码
        String finalUrl = builder.build().toUriString();
        log.info("获取知识库文档列表请求，知识库ID: {}, 参数: {}, 最终URL: {}", datasetId, params, finalUrl);
        
        HttpEntity<?> entity = new HttpEntity<>(getHeaders());
        
        ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
            finalUrl, 
            HttpMethod.GET, 
            entity, 
            RagFlowResponse.class
        );
        
        RagFlowResponse<?> response = responseEntity.getBody();
        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("获取知识库文档列表操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1);
                response.setMessage(dataStr);
                return response;
            }
        }
        
        // 过滤响应数据，只返回指定字段
        return filterDocumentResponse(response);
    }

    @Override
    public RagFlowResponse<?> listDocumentsFilter(String datasetId, Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
                baseUrl + "/api/v1/datasets/" + datasetId + "/documents"
        );

        HttpEntity<?> entity = new HttpEntity<>(getHeaders());

        log.info("获取知识库文档列表请求，知识库ID: {}, 参数: {}", datasetId, params);
        ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
                builder.toUriString(),
                HttpMethod.GET,
                entity,
                RagFlowResponse.class
        );

        //改成边获取数据边记录个数
        RagFlowResponse<?> response = responseEntity.getBody();
        Map<String, List<Map<String, Object>>> data = (Map<String, List<Map<String, Object>>>) response.getData();

        for (Map.Entry<String, List<Map<String, Object>>> entry : data.entrySet()) {
            if(entry.getKey().equals("total"))break;
            List<Map<String, Object>> list = entry.getValue();
            List<Map<String, Object>> newList = new ArrayList<>(); // 创建新列表
            for (int i = 0; i < list.size() ; i++) {
                newList.add(list.get(i)); // 添加需要保留的元素
            }
            entry.setValue(newList); // 更新原条目的值为新列表
        }

        RagFlowResponse< Map<String, List<Map<String, Object>>>> responseNew = responseEntity.getBody() ;

        int page = (int) params.get("page");
        int pageSize = (int) params.get("page_size");
        int start = (page - 1) * pageSize;
        int end = start + pageSize - 1;
        data = (Map<String, List<Map<String, Object>>>) response.getData();
        for (Map.Entry<String, List<Map<String, Object>>> entry : data.entrySet()) {
            if (entry.getKey().equals("total")) break;
            List<Map<String, Object>> list = entry.getValue();
            List<Map<String, Object>> newList = new ArrayList<>(); // 创建新列表

            int actualEnd = Math.min(end, list.size() - 1);
            if (start < list.size()) {
                for (int i = start; i <= actualEnd; i++) {
                    newList.add(list.get(i)); // 添加需要保留的元素
                }
            }
            entry.setValue(newList); // 更新原条目的值为新列表
        }
        responseNew.setData(data);

        if (responseNew != null && responseNew.getCode() != null && responseNew.getCode() != 0) {
            Object dataStr = responseNew.getData();
            if (dataStr != null) {
                log.warn("获取知识库文档列表操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", responseNew.getCode(), responseNew.getMessage(), dataStr);
                responseNew.setCode(-1);
                responseNew.setMessage(dataStr.toString());
                return responseNew;
            }
        }

        // 过滤响应数据，只返回指定字段
        return filterDocumentResponse(responseNew);
    }
    @Override
    public RagFlowResponse<?> deleteDocuments(String datasetId, List<String> ids) {
        // 添加关键参数验证，防止意外删除所有文档
        if (ids == null || ids.isEmpty()) {
            log.error("删除知识库文档失败：文档ID列表为空，知识库ID: {}", datasetId);
            // 创建错误响应而不是发送请求
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("文档ID列表不能为空");
            errorResponse.setData(null);
            return errorResponse;
        }
        
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents";
        
        Map<String, List<String>> requestMap = new HashMap<>();
        requestMap.put("ids", ids);
        
        HttpEntity<Map<String, List<String>>> entity = new HttpEntity<>(requestMap, getHeaders());
        
        log.info("删除知识库文档请求，知识库ID: {}, 文档IDs: {}", datasetId, ids);
        ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
            url, 
            HttpMethod.DELETE, 
            entity, 
            RagFlowResponse.class
        );
        
        RagFlowResponse<?> response = responseEntity.getBody();
        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("删除知识库文档操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1);
                response.setMessage(dataStr);
            }
        }
        return response;
    }
    
    @Override
    public RagFlowResponse<?> uploadDocuments(String datasetId, List<byte[]> files, List<String> fileNames) {
        if (files == null || fileNames == null || files.size() != fileNames.size()) {
            throw new IllegalArgumentException("文件数据和文件名列表长度不匹配");
        }
        
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents";
        
        // 创建multipart请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("Authorization", "Bearer " + apiKey);
        
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        
        // 为每个文件创建ByteArrayResource并添加到body中
        for (int i = 0; i < files.size(); i++) {
            final String fileName = fileNames.get(i);
            final byte[] fileData = files.get(i);
            
            ByteArrayResource resource = new ByteArrayResource(fileData) {
                @Override
                public String getFilename() {
                    return fileName;
                }
            };
            
            // 添加多个file参数
            body.add("file", resource);
        }
        
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);
        
        log.info("批量上传文档到知识库请求，知识库ID: {}, 文件数量: {}, 文件名: {}", 
                datasetId, files.size(), fileNames);
        RagFlowResponse<?> response = restTemplate.postForObject(url, entity, RagFlowResponse.class);

        if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
            String dataStr = (String) response.getData();
            if (dataStr != null && !dataStr.isEmpty()) {
                log.warn("批量上传文档操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", 
                        response.getCode(), response.getMessage(), dataStr);
                response.setCode(-1);
                response.setMessage(dataStr);
                return response;
            }
        }
        
        // 过滤响应数据，只返回指定字段
        return filterDocumentResponse(response);
    }
    
    @Override
    public DocumentDownloadResult downloadDocument(String datasetId, String documentId) {
        // 首先尝试获取文档信息以获得原始文件名
        String originalFileName = getDocumentName(documentId);
        
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents/" + documentId;
        
        // 创建请求头，只需要Authorization
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + apiKey);
        
        HttpEntity<?> entity = new HttpEntity<>(headers);
        
        log.info("下载知识库文档请求，知识库ID: {}, 文档ID: {}", datasetId, documentId);
        
        try {
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                url, 
                HttpMethod.GET, 
                entity, 
                byte[].class
            );
            
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                byte[] fileContent = responseEntity.getBody();
                if (fileContent == null) {
                    throw new RuntimeException("文档内容为空");
                }
                
                // 使用获取到的原始文件名，如果没有则从响应头获取
                String fileName = originalFileName;
                if (fileName == null || fileName.trim().isEmpty()) {
                    fileName = extractFileNameFromHeaders(responseEntity.getHeaders());
                }
                if (fileName == null || fileName.trim().isEmpty()) {
                    fileName = "document_" + documentId + ".txt"; // 默认使用.txt扩展名
                }
                
                // 根据文件扩展名设置内容类型
                String contentType = determineContentType(fileName);
                
                DocumentDownloadResult result = new DocumentDownloadResult(
                    fileContent, 
                    fileName, 
                    fileContent.length, 
                    contentType
                );
                
                log.info("文档下载成功，知识库ID: {}, 文档ID: {}, 文件名: {}, 文件大小: {} bytes", 
                        datasetId, documentId, fileName, fileContent.length);
                        
                return result;
            } else {
                log.error("文档下载失败，HTTP状态码: {}, 知识库ID: {}, 文档ID: {}", 
                         responseEntity.getStatusCode(), datasetId, documentId);
                throw new RuntimeException("文档下载失败，HTTP状态码: " + responseEntity.getStatusCode());
            }
        } catch (Exception e) {
            log.error("下载知识库文档时发生异常，知识库ID: {}, 文档ID: {}", datasetId, documentId, e);
            throw new RuntimeException("文档下载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取文档名称
     */
    private String getDocumentName(String documentId) {
        try {
            // 从数据库查询文档信息
            DocumentEntity document = documentRepository.findById(documentId)
                    .orElse(null);
            
            if (document != null && StringUtils.hasText(document.getName())) {
                return document.getName();
            }
            
            log.warn("未找到文档或文档名称为空，文档ID: {}", documentId);
        } catch (Exception e) {
            log.warn("从数据库获取文档名称失败，将使用默认名称，文档ID: {}, 错误: {}",
                    documentId, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 根据文件名确定内容类型
     */
    private String determineContentType(String fileName) {
        if (fileName == null) {
            return MediaType.APPLICATION_OCTET_STREAM_VALUE;
        }
        
        String lowerFileName = fileName.toLowerCase();
        
        // 常见文档类型
        if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (lowerFileName.endsWith(".doc")) {
            return "application/msword";
        } else if (lowerFileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerFileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerFileName.endsWith(".pptx")) {
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
        } else if (lowerFileName.endsWith(".ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (lowerFileName.endsWith(".txt")) {
            return "text/plain";
        } else if (lowerFileName.endsWith(".rtf")) {
            return "application/rtf";
        } else if (lowerFileName.endsWith(".csv")) {
            return "text/csv";
        } else if (lowerFileName.endsWith(".json")) {
            return "application/json";
        } else if (lowerFileName.endsWith(".xml")) {
            return "application/xml";
        } else if (lowerFileName.endsWith(".html") || lowerFileName.endsWith(".htm")) {
            return "text/html";
        } else {
            return MediaType.APPLICATION_OCTET_STREAM_VALUE;
        }
    }
    
    /**
     * 从响应头中提取文件名
     */
    private String extractFileNameFromHeaders(HttpHeaders headers) {
        // 尝试从Content-Disposition头获取文件名
        String contentDisposition = headers.getFirst("Content-Disposition");
        if (contentDisposition != null && contentDisposition.contains("filename=")) {
            String[] parts = contentDisposition.split("filename=");
            if (parts.length > 1) {
                String fileName = parts[1].trim();
                // 移除引号
                if (fileName.startsWith("\"") && fileName.endsWith("\"")) {
                    fileName = fileName.substring(1, fileName.length() - 1);
                }
                return fileName;
            }
        }
        return null;
    }
    
    @Override
    @Transactional
    public ApiResp<?> updateDocument(String datasetId, String documentId, UpdateDocumentRequest request) {


        if (StringUtils.hasText(request.getName()) || StringUtils.hasText(request.getChunk_method())) {
            String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents/" + documentId;
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<UpdateDocumentRequest> entity = new HttpEntity<>(request, headers);

            log.info("更新文档配置请求: datasetId={}, documentId={}, requestBody={}", datasetId, documentId, request);

            // 调用RagFlow API
            ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.PUT,
                    entity,
                    RagFlowResponse.class
            );

            RagFlowResponse<?> ragFlowResponse = responseEntity.getBody();

            // 检查并处理实际的错误情况
            if (ragFlowResponse != null && ragFlowResponse.getCode() != null && ragFlowResponse.getCode() != 0 && ragFlowResponse.getData() instanceof String) {
                String dataStr = (String) ragFlowResponse.getData();
                if (dataStr != null && !dataStr.isEmpty()) {
                    log.warn("更新文档配置操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", ragFlowResponse.getCode(), ragFlowResponse.getMessage(), dataStr);
                    return ApiResp.error(-1, "更新文档配置失败：" + dataStr);
                }
            }
        }

        // 如果RagFlow API调用成功，并且提供了有效的status，则更新本地数据库
        if (StringUtils.hasText(request.getStatus())) {
            try {
                // 使用新的字段名 kbId 和 id 进行查询
                DocumentEntity documentEntity = documentRepository.findByKbIdAndId(datasetId, documentId)
                        .orElseGet(() -> {
                            log.warn("在数据库中未找到文档 kbId: {}, id: {}", datasetId, documentId);
                            return null;
                        });

                if (request.getStatus() != null && documentEntity != null && !request.getStatus().equals(documentEntity.getStatus())) {
                    documentEntity.setStatus(request.getStatus());
                    documentRepository.save(documentEntity);
                }

            } catch (Exception e) {
                log.error("更新本地数据库中的文档状态失败: kbId={}, id={}, request={}", datasetId, documentId, request, e);
            }
        }

        return ApiResp.success("知识库文档更新成功", null);
    }

    @Async
    @Override
    public ApiResp<?> cloneDocument(String sourceId, String targetId) {
        log.info("开始克隆知识库，源ID: {}, 目标ID: {}", sourceId, targetId);
        int successCount = 0;
        int failCount = 0;
        List<String> failFiles = new ArrayList<>();
        List<String> uploadedDocIds = new ArrayList<>();
        List<String> uploadedFileNames = new ArrayList<>();
        try {
            // 1. 获取源知识库所有文档
            Map<String, Object> params = new HashMap<>();
            params.put("page", 1);
            params.put("page_size", 1000); // 假设单库文档不会超1000
            RagFlowResponse<?> listResp = listDocuments(sourceId, params);
            List<FilteredDocumentResponse> docs = new ArrayList<>();
            if (listResp != null && listResp.getCode() != null && listResp.getCode() == 0 && listResp.getData() != null) {
                Object data = listResp.getData();
                if (data instanceof FilteredDocumentListResponse) {
                    docs = ((FilteredDocumentListResponse) data).getDocs();
                } else if (data instanceof List) {
                    docs = (List<FilteredDocumentResponse>) data;
                }
            }
            if (docs == null || docs.isEmpty()) {
                log.warn("源知识库无文档，无需克隆");
                return ApiResp.success("源知识库无文档", null);
            }
            // 2. 遍历文档，下载并上传
            for (FilteredDocumentResponse doc : docs) {
                try {
                    DocumentDownloadResult download = downloadDocument(sourceId, doc.getId());
                    if (download == null || download.getContent() == null || download.getContent().length == 0) {
                        log.warn("文档下载失败，ID: {}", doc.getId());
                        failCount++;
                        failFiles.add(doc.getName());
                        continue;
                    }
                    RagFlowResponse<?> uploadResp = uploadDocument(targetId, download.getContent(), download.getFileName());
                    if (uploadResp != null && uploadResp.getCode() != null && uploadResp.getCode() == 0) {
                        // 提取新文档ID
                        String newDocId = extractDocumentIdFromUploadResponse(uploadResp);
                        if (newDocId != null) {
                            // 更新新文档的parser_id和parser_config字段
                            updateDocumentParserConfig(doc.getId(), newDocId);
                        }
                        uploadedDocIds.add(newDocId);
                        uploadedFileNames.add(download.getFileName());
                        successCount++;
                    } else {
                        log.warn("文档上传失败，ID: {}，msg: {}", doc.getId(), uploadResp != null ? uploadResp.getMessage() : "null");
                        failCount++;
                        failFiles.add(doc.getName());
                    }
                } catch (Exception e) {
                    log.error("克隆单个文档异常，ID: {}", doc.getId(), e);
                    failCount++;
                    failFiles.add(doc.getName());
                }
            }
            // 3. 上传完成后，触发解析
            if (!uploadedDocIds.isEmpty()) {
                ParseDocumentsRequest parseRequest = new ParseDocumentsRequest();
                parseRequest.setDocument_ids(uploadedDocIds);
                RagFlowResponse<?> parseResp = parseDocuments(targetId, parseRequest);
                if (parseResp != null && parseResp.getCode() != null && parseResp.getCode() == 0) {
                    log.info("批量文档自动解析触发成功，目标知识库ID: {}, 文档数量: {}, 文档IDs: {}", targetId, uploadedDocIds.size(), uploadedDocIds);
                } else {
                    log.warn("批量文档自动解析触发失败，目标知识库ID: {}, 文档数量: {}, 文档IDs: {}, 响应: {}", targetId, uploadedDocIds.size(), uploadedDocIds, parseResp);
                }
            }
            String msg = String.format("克隆完成，成功: %d，失败: %d", successCount, failCount);
            if (!failFiles.isEmpty()) {
                msg += "，失败文件: " + String.join(",", failFiles);
            }
            return ApiResp.success(msg, null);
        } catch (Exception e) {
            log.error("克隆知识库异常", e);
            return ApiResp.error(-1, "克隆知识库异常: " + e.getMessage());
        }
    }

    // ---- Chunk Management API Implementations ----

    @Override
    public RagFlowResponse<?> addChunkToDocument(String datasetId, String documentId, AddChunkToDocumentRequest request) {
        // 使用了第二张截图中的API: /api/v1/datasets/{dataset_id}/documents/{document_id}/chunks
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents/" + documentId + "/chunks";
        HttpEntity<AddChunkToDocumentRequest> entity = new HttpEntity<>(request, getHeaders());

        log.info("向文档添加分段请求，URL: {}, Body: {}", url, request);
        try {
            RagFlowResponse<?> response = restTemplate.postForObject(url, entity, RagFlowResponse.class);
            // 考虑是否需要为chunk响应添加特定的过滤逻辑
            return response;
        } catch (HttpClientErrorException e) {
            log.error("向文档添加分段失败 (HTTP {}): {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString(), request, e);
            return new RagFlowResponse<>(-1, "向文档添加分段失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("向文档添加分段时发生未知错误: {}, Body: {}", e.getMessage(), request, e);
            return new RagFlowResponse<>(-1, "向文档添加分段时发生未知错误: " + e.getMessage(), null);
        }
    }

    @Override
    public RagFlowResponse<?> listChunks(String datasetId, String documentId, Map<String, Object> params) {
        // 根据接口文档，使用完整的路径：/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
            baseUrl + "/api/v1/datasets/" + datasetId + "/documents/" + documentId + "/chunks"
        );

        // 添加查询参数
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                builder.queryParam(entry.getKey(), entry.getValue());
            }
        }

        HttpEntity<?> entity = new HttpEntity<>(getHeaders());
        log.info("列出分段请求，知识库ID: {}, 文档ID: {}, URL: {}", datasetId, documentId, builder.toUriString());
        try {
            ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
                builder.toUriString(),
                HttpMethod.GET,
                entity,
                RagFlowResponse.class
            );
            
            RagFlowResponse<?> response = responseEntity.getBody();
            
            // 过滤响应数据，只返回指定字段
            return filterChunkResponse(response);
        } catch (HttpClientErrorException e) {
            log.error("列出分段失败 (HTTP {}): {}, 知识库ID: {}, 文档ID: {}, URL: {}", 
                     e.getStatusCode(), e.getResponseBodyAsString(), datasetId, documentId, builder.toUriString(), e);
            return new RagFlowResponse<>(-1, "列出分段失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("列出分段时发生未知错误: {}, 知识库ID: {}, 文档ID: {}, URL: {}", 
                     e.getMessage(), datasetId, documentId, builder.toUriString(), e);
            return new RagFlowResponse<>(-1, "列出分段时发生未知错误: " + e.getMessage(), null);
        }
    }

    @Override
    public RagFlowResponse<?> deleteChunks(String datasetId, String documentId, DeleteChunksRequest request) {
        // 根据接口文档，使用完整的路径：/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents/" + documentId + "/chunks";
        
        HttpEntity<DeleteChunksRequest> entity = new HttpEntity<>(request, getHeaders());
        log.info("删除分段请求, 知识库ID: {}, 文档ID: {}, URL: {}, Body: {}", datasetId, documentId, url, request);
        try {
            ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.DELETE, 
                entity,
                RagFlowResponse.class
            );
            return responseEntity.getBody();
        } catch (HttpClientErrorException e) {
            log.error("删除分段失败 (HTTP {}): {}, 知识库ID: {}, 文档ID: {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString(), datasetId, documentId, request, e);
            return new RagFlowResponse<>(-1, "删除分段失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("删除分段时发生未知错误: {}, 知识库ID: {}, 文档ID: {}, Body: {}", e.getMessage(), datasetId, documentId, request, e);
            return new RagFlowResponse<>(-1, "删除分段时发生未知错误: " + e.getMessage(), null);
        }
    }

    @Override
    public RagFlowResponse<?> updateChunkInDocument(String datasetId, String documentId, String chunkId, UpdateChunkRequest request) {
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/documents/" + documentId + "/chunks/" + chunkId;
        
        HttpEntity<UpdateChunkRequest> entity = new HttpEntity<>(request, getHeaders());
        log.info("更新文档分段请求, URL: {}, Body: {}", url, request);
        try {
            ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.PUT, // Correct HTTP method as per RAGFlow docs
                entity,
                RagFlowResponse.class
            );
            return responseEntity.getBody();
        } catch (HttpClientErrorException e) {
            log.error("更新文档分段失败 (HTTP {}): {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString(), request, e);
            return new RagFlowResponse<>(-1, "更新文档分段失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("更新文档分段时发生未知错误: {}, Body: {}", e.getMessage(), request, e);
            return new RagFlowResponse<>(-1, "更新文档分段时发生未知错误: " + e.getMessage(), null);
        }
    }

    /**
     * 从知识库创建响应中提取知识库ID
     */
    private String extractDatasetIdFromResponse(RagFlowResponse<?> response) {
        try {
            Object data = response.getData();
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                return (String) dataMap.get("id");
            }
        } catch (Exception e) {
            log.error("提取知识库ID失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 为指定知识库创建对应的聊天助手
     */
    @Transactional
    private void createChatAssistantForDataset(String datasetName, String datasetId) {
        // 记录原始的统计数据
        KnowledgebaseEntity originalKb = null;
        boolean needRestore = false;
        
        try {
            // 1. 查询知识库当前的统计数据，如果不存在则创建
            originalKb = knowledgebaseRepository.findById(datasetId).orElse(null);
            if (originalKb == null) {
                throw new RuntimeException("知识库数据异常！");
            }
            
            // 2. 如果知识库存在且统计数据为0，则临时修改为1
            if (originalKb.getDocNum() == 0 || originalKb.getTokenNum() == 0 || originalKb.getChunkNum() == 0) {
                
                log.info("知识库 {} 统计数据为空，临时修改为1以创建聊天助手", datasetId);
                
                // 临时设置为1
                int updateCount = knowledgebaseRepository.updateStatistics(datasetId, 1, 1L, 1);
                if (updateCount > 0) {
                    needRestore = true;
                    log.info("已临时修改知识库 {} 的统计数据为1", datasetId);
                }
            }
            
            // 3. 构建聊天助手请求，只传递必要的两个参数
            ChatAssistantRequest chatRequest = new ChatAssistantRequest();
            chatRequest.setName(datasetName + "-聊天助手"); // datasetName 已经带时间戳
            chatRequest.setDataset_ids(Arrays.asList(datasetId));
            Map<String, Object> prompt = new HashMap<>();
            prompt.put("opener","你好！ 我是你的助理，有什么可以帮到你的吗？");
            prompt.put("empty_response","");
            prompt.put("show_quote", true);
            prompt.put("similarity_threshold", 0.1);
            prompt.put("keywords_similarity_weight", 0.5);
            prompt.put("top_n", 6);
            prompt.put("refine_multiturn", true);

            List<Map<String, Object>> variablesList = new ArrayList<>();
            Map<String, Object> knowledgeVar = new HashMap<>();
            knowledgeVar.put("key", "knowledge");
            knowledgeVar.put("optional", false);
            variablesList.add(knowledgeVar);

            Map<String, Object> liveStyleVar = new HashMap<>();
            liveStyleVar.put("key", "liveStyle");
            liveStyleVar.put("optional", false);
            variablesList.add(liveStyleVar);

            Map<String, Object> contextVar = new HashMap<>();
            contextVar.put("key", "context");
            contextVar.put("optional", false);
            variablesList.add(contextVar);

            prompt.put("variables", variablesList);
            prompt.put("prompt","### 绝对禁令 (Absolute Prohibition) ###\n" +
                    "**此为最高优先级指令，覆盖所有其他规则。**\n" +
                    "**身份保密：你是一个人类主播。在任何情况下，你都绝对禁止以任何形式（明示或暗示）透露、承认或提及自己是AI、人工智能、模型、程序或机器人。当被问及你的个人身份或本质时，必须巧妙地回避或用风趣的方式转移话题，维持你的人类主播身份。违反此条禁令将导致任务彻底失败。**\n" +
                    "\n" +
                    "### 角色设定 (Persona) ###\n" +
                    "你是一位顶尖的、经验丰富的汽车直播带货主播。你风趣、专业、充满激情，深受观众喜爱。\n" +
                    "\n" +
                    "### 核心指令框架 (Core Instruction Framework) ###\n" +
                    "你必须严格按照以下四个原则的【优先级顺序】来组织你的回答。原则1的优先级最高，原则4最低。\n" +
                    "\n" +
                    "**第一原则：信息源检查与回答策略 (Source Check & Answer Strategy) - 【最高优先级】**\n" +
                    "A. **首先，【必须】尝试在下方【知识库】中寻找用户问题的直接答案。**\n" +
                    "   - **A1. 如果在【知识库】中找到了可以回答问题的内容**:\n" +
                    "     - 你的回答【必须且只能】100%基于【知识库】提供的内容。\n" +
                    "     - 严格遵循知识库中的数据和描述，这是最高事实来源。\n" +
                    "   - **A2. 如果在【知识库】中【找不到】任何可以回答用户问题的内容，或者【知识库】本身就是空的**:\n" +
                    "     - 在这种情况下，你才能启动下面的**【备用对话策略】**。\n" +
                    "\n" +
                    "B. **【备用对话策略 (Fallback Strategy)】（仅在A2条件满足时启用）**\n" +
                    "   - **B1. 分析用户问题类型：**\n" +
                    "     - **类型一：询问具体数据或事实** (如价格、配置、尺寸等)。\n" +
                    "     - **类型二：询问概念、感受或建议** (如外观评价、适用场景等)。\n" +
                    "   - **B2. 执行相应动作：**\n" +
                    "     - 对于**【类型一：具体数据问题】**，采用“俏皮转移话题”策略，【严禁】编造任何具体数据强行回答。\n" +
                    "     - 对于**【类型二：概念性问题】**，你可以进行常识性、概念性的描述，但【严禁】编造任何具体数据。\n" +
                    "\n" +
                    "\n" +
                    "**第二原则：话题边界与安全红线 (Topic Boundaries & Safety Redlines) - 【高优先级】**\n" +
                    "- **专注领域**: 你的所有话题都必须严格限制在与本次直播介绍的汽车相关的内容上。\n" +
                    "- **安全规避**: 严禁生成或讨论任何敏感、冒犯性、不当或非法内容。\n" +
                    "- **无关/敏感问题处理策略**: 如果用户的问题与汽车完全无关，或触及了安全红线，你【必须】采用“俏皮转移话题”策略，在30个字符内用直播话术巧妙地把话题拉回来。\n" +
                    "\n" +
                    "**第三原则：输出格式化铁律 (Output Formatting Rules) - 【中优先级】**\n" +
                    "- **数字转读音**: 最终输出的文本中【绝不能】出现任何阿拉伯数字 (0, 1, 2, 3...)。所有数字都必须转换成符合口语习惯的中文大写读音。\n" +
                    "- **转换示例**: \"129999\" -> “十二万九千九百九十九”；\"2025\" -> “二零二五”。\n" +
                    "- **数据源与转换**: 你从【知识库】中提取原始数字，在脑海中完成转换，最后只输出转换后的中文读音。\n" +
                    "\n" +
                    "**第四原则：直播风格与个性 (Live-stream Style & Personality) - 【特化修改】**\n" +
                    "{liveStyle}\n" +
                    "\n" +
                    "---\n" +
                    "\n" +
                    "### 任务执行区 (Execution Zone) ###\n" +
                    "=== 以下是本场直播上下文 ===\n" +
                    "{context}\n" +
                    "=== 以上是本场直播上下文 ===\n" +
                    "\n" +
                    "=== 以下是知识库 ===\n" +
                    "{knowledge}\n" +
                    "=== 以上是知识库 ===");
            chatRequest.setPrompt(prompt);
            // 4. 调用创建聊天助手API
            RagFlowResponse<?> chatResponse = createChatAssistant(chatRequest);
            if (chatResponse != null && chatResponse.getCode() != null && chatResponse.getCode() == 0) {
                log.info("聊天助手创建成功，知识库: {}, 助手名称: {}", datasetId, chatRequest.getName());
            } else {
                log.error("聊天助手创建失败，知识库: {}, 响应: {}", datasetId, chatResponse);
            }
            
        } catch (Exception e) {
            log.error("为知识库 {} 创建聊天助手时发生异常: {}", datasetId, e.getMessage(), e);
        } finally {
            // 5. 恢复原始的统计数据
            if (needRestore && originalKb != null) {
                try {
                    int restoreCount = knowledgebaseRepository.updateStatistics(
                        datasetId, 
                        originalKb.getDocNum(), 
                        originalKb.getTokenNum(), 
                        originalKb.getChunkNum()
                    );
                    if (restoreCount > 0) {
                        log.info("已恢复知识库 {} 的原始统计数据: docNum={}, tokenNum={}, chunkNum={}", 
                                datasetId, originalKb.getDocNum(), originalKb.getTokenNum(), originalKb.getChunkNum());
                    }
                } catch (Exception restoreException) {
                    log.error("恢复知识库 {} 统计数据失败: {}", datasetId, restoreException.getMessage(), restoreException);
                }
            }
        }
    }

    @Override
    public RagFlowResponse<?> createChatAssistant(ChatAssistantRequest request) {
        String url = baseUrl + "/api/v1/chats";
        HttpEntity<ChatAssistantRequest> entity = new HttpEntity<>(request, getHeaders());
        
        log.info("创建聊天助手请求: {}", request);
        try {
            RagFlowResponse<?> response = restTemplate.postForObject(url, entity, RagFlowResponse.class);

            // 检查并处理实际的错误情况
            if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
                String dataStr = (String) response.getData();
                if (dataStr != null && !dataStr.isEmpty()) {
                    log.warn("创建聊天助手操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                    response.setCode(-1); // 设置为通用错误码
                    response.setMessage(dataStr); // 将错误信息设置到 message 字段
                    return response;
                }
            }
            
            log.info("聊天助手创建成功，响应: {}", response);
            return response;
        } catch (HttpClientErrorException e) {
            log.error("创建聊天助手失败 (HTTP {}): {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString(), request, e);
            return new RagFlowResponse<>(-1, "创建聊天助手失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("创建聊天助手时发生未知错误: {}, Body: {}", e.getMessage(), request, e);
            return new RagFlowResponse<>(-1, "创建聊天助手时发生未知错误: " + e.getMessage(), null);
        }
    }

    @Override
    public RagFlowResponse<?> deleteChatAssistants(List<String> ids) {
        // 添加关键参数验证，防止意外删除所有聊天助手
        if (ids == null || ids.isEmpty()) {
            log.error("删除聊天助手失败：聊天助手ID列表为空");
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("聊天助手ID列表不能为空");
            errorResponse.setData(null);
            return errorResponse;
        }
        
        String url = baseUrl + "/api/v1/chats";
        
        Map<String, List<String>> requestMap = new HashMap<>();
        requestMap.put("ids", ids);
        
        HttpEntity<Map<String, List<String>>> entity = new HttpEntity<>(requestMap, getHeaders());
        
        log.info("删除聊天助手请求，IDs: {}", ids);
        try {
            ResponseEntity<RagFlowResponse> responseEntity = restTemplate.exchange(
                url, 
                HttpMethod.DELETE, 
                entity, 
                RagFlowResponse.class
            );
            
            RagFlowResponse<?> response = responseEntity.getBody();
            if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
                String dataStr = (String) response.getData();
                if (dataStr != null && !dataStr.isEmpty()) {
                    log.warn("删除聊天助手操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                    response.setCode(-1);
                    response.setMessage(dataStr);
                }
            }
            return response;
        } catch (HttpClientErrorException e) {
            log.error("删除聊天助手失败 (HTTP {}): {}, IDs: {}", e.getStatusCode(), e.getResponseBodyAsString(), ids, e);
            return new RagFlowResponse<>(-1, "删除聊天助手失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("删除聊天助手时发生未知错误: {}, IDs: {}", e.getMessage(), ids, e);
            return new RagFlowResponse<>(-1, "删除聊天助手时发生未知错误: " + e.getMessage(), null);
        }
    }

    @Override
    public List<QAUploadResponse> uploadQAToDataset(String datasetId, List<QAUploadRequest> qaList) {
        try {
            // 1. 检查知识库中是否已存在QA类型的文档
            String existingQADocumentId = findExistingQADocument(datasetId);
            
            if (existingQADocumentId != null) {
                // 如果存在QA文档，直接添加分段
                log.info("发现已存在的QA文档，文档ID: {}，将直接添加分段", existingQADocumentId);
                return addQAChunksToExistingDocumentAndReturnResults(datasetId, existingQADocumentId, qaList);
            } else {
                // 如果不存在QA文档，创建新的CSV文档
                log.info("未发现QA文档，将创建新的QA文档");
                return createNewQADocumentAndReturnResults(datasetId, qaList);
            }
            
        } catch (Exception e) {
            log.error("上传QA到知识库时发生异常，知识库ID: {}", datasetId, e);
            throw new RuntimeException("上传QA时发生异常: " + e.getMessage(), e);
        }
    }

    @Override
    public RagFlowResponse<?> parseDocuments(String datasetId, ParseDocumentsRequest request) {
        String url = baseUrl + "/api/v1/datasets/" + datasetId + "/chunks";
        HttpEntity<ParseDocumentsRequest> entity = new HttpEntity<>(request, getHeaders());
        
        log.info("解析文档请求，知识库ID: {}, 请求: {}", datasetId, request);
        try {
            RagFlowResponse<?> response = restTemplate.postForObject(url, entity, RagFlowResponse.class);

            // 检查并处理实际的错误情况
            if (response != null && response.getCode() != null && response.getCode() != 0 && response.getData() instanceof String) {
                String dataStr = (String) response.getData();
                if (dataStr != null && !dataStr.isEmpty()) {
                    log.warn("解析文档操作指示成功 (code: {}) 但data字段包含潜在错误信息. Original message: '{}', data: '{}'", response.getCode(), response.getMessage(), dataStr);
                    response.setCode(-1);
                    response.setMessage(dataStr);
                    return response;
                }
            }
            
            log.info("文档解析成功，知识库ID: {}, 响应: {}", datasetId, response);
            return response;
        } catch (HttpClientErrorException e) {
            log.error("解析文档失败 (HTTP {}): {}, 知识库ID: {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString(), datasetId, request, e);
            return new RagFlowResponse<>(-1, "解析文档失败: " + e.getResponseBodyAsString(), null);
        } catch (Exception e) {
            log.error("解析文档时发生未知错误: {}, 知识库ID: {}, Body: {}", e.getMessage(), datasetId, request, e);
            return new RagFlowResponse<>(-1, "解析文档时发生未知错误: " + e.getMessage(), null);
        }
    }

    /**
     * 创建QA CSV内容
     */
    private String createQACSV(List<QAUploadRequest> qaList) {
        StringBuilder csvContent = new StringBuilder();
        
        for (QAUploadRequest qa : qaList) {
            // 处理CSV中的特殊字符，如果包含逗号、引号或换行符，需要用引号包围并转义
            String question = escapeCSVField(qa.getQuestion());
            String answer = escapeCSVField(qa.getAnswer());
            
            csvContent.append(question).append(",").append(answer).append("\n");
        }
        
        return csvContent.toString();
    }

    /**
     * 转义CSV字段中的特殊字符
     */
    private String escapeCSVField(String field) {
        if (field == null) {
            return "";
        }
        
        // 如果字段包含逗号、引号或换行符，需要用引号包围
        if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
            // 转义引号（双引号变成两个双引号）
            String escaped = field.replace("\"", "\"\"");
            return "\"" + escaped + "\"";
        }
        
        return field;
    }

    /**
     * 从上传响应中提取文档ID
     */
    private String extractDocumentIdFromUploadResponse(RagFlowResponse<?> response) {
        try {
            Object data = response.getData();
            
            // 处理FilteredDocumentResponse对象
            if (data instanceof FilteredDocumentResponse) {
                FilteredDocumentResponse doc = (FilteredDocumentResponse) data;
                return doc.getId();
            }
            // 处理FilteredDocumentResponse列表
            else if (data instanceof List) {
                List<?> dataList = (List<?>) data;
                if (!dataList.isEmpty() && dataList.get(0) instanceof FilteredDocumentResponse) {
                    FilteredDocumentResponse doc = (FilteredDocumentResponse) dataList.get(0);
                    return doc.getId();
                }
            }
            // 处理原始Map格式（兼容性）
            else if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                return (String) dataMap.get("id");
            }
            
            log.warn("无法识别的响应数据格式: {}", data != null ? data.getClass().getSimpleName() : "null");
        } catch (Exception e) {
            log.error("提取文档ID失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 查找知识库中已存在的QA类型文档（从数据库查询）
     */
    private String findExistingQADocument(String datasetId) {
        try {
            // 从数据库中查询parser_id为"qa"的文档
            List<DocumentEntity> qaDocuments = documentRepository.findByKbIdAndParserId(datasetId, "qa");
            
            if (!qaDocuments.isEmpty()) {
                // 返回第一个找到的QA文档ID
                DocumentEntity qaDoc = qaDocuments.get(0);
                log.info("从数据库找到已存在的QA文档，ID: {}, 名称: {}, 知识库ID: {}", 
                        qaDoc.getId(), qaDoc.getName(), datasetId);
                return qaDoc.getId();
            }
            
            log.info("数据库中未找到QA类型文档，知识库ID: {}", datasetId);
            return null;
            
        } catch (Exception e) {
            log.error("从数据库查找QA文档时发生异常，知识库ID: {}", datasetId, e);
            return null;
        }
    }

    /**
     * 向已存在的QA文档添加分段并返回结果
     */
    private List<QAUploadResponse> addQAChunksToExistingDocumentAndReturnResults(String datasetId, String documentId, List<QAUploadRequest> qaList) {
        try {
            List<QAUploadResponse> uploadedQAs = new ArrayList<>();
            
            for (QAUploadRequest qa : qaList) {
                // 创建QA分段内容，格式：Question: xxx[TAB]Answer: xxx
                String chunkContent = "Question: " + qa.getQuestion() + "\tAnswer: " + qa.getAnswer();
                
                // 创建添加分段请求
                AddChunkToDocumentRequest chunkRequest = new AddChunkToDocumentRequest();
                chunkRequest.setContent(chunkContent);
                
                // 调用添加分段API
                RagFlowResponse<?> chunkResponse = addChunkToDocument(datasetId, documentId, chunkRequest);
                
                if (chunkResponse != null && chunkResponse.getCode() != null && chunkResponse.getCode() == 0) {
                    // 从响应中提取chunk ID
                    String chunkId = extractChunkIdFromResponse(chunkResponse);
                    if (chunkId != null) {
                        QAUploadResponse uploadedQA = new QAUploadResponse();
                        uploadedQA.setId(chunkId);
                        uploadedQA.setQuestion(qa.getQuestion());
                        uploadedQA.setAnswer(qa.getAnswer());
                        uploadedQAs.add(uploadedQA);
                        log.debug("QA分段添加成功: {}, ID: {}", qa.getQuestion(), chunkId);
                    }
                } else {
                    log.warn("QA分段添加失败: {}, 响应: {}", qa.getQuestion(), chunkResponse);
                }
            }
            
            log.info("QA分段添加完成，知识库ID: {}, 文档ID: {}, 成功: {}", 
                    datasetId, documentId, uploadedQAs.size());
            
            return uploadedQAs;
            
        } catch (Exception e) {
            log.error("向已存在文档添加QA分段时发生异常，知识库ID: {}, 文档ID: {}", datasetId, documentId, e);
            throw new RuntimeException("添加QA分段时发生异常: " + e.getMessage(), e);
        }
    }

    /**
     * 创建新的QA文档并返回结果
     */
    private List<QAUploadResponse> createNewQADocumentAndReturnResults(String datasetId, List<QAUploadRequest> qaList) {
        try {
            // 1. 创建CSV文件
            String csvContent = createQACSV(qaList);
            String fileName = "QA数据集.csv";
            
            // 2. 上传CSV文件到知识库
            RagFlowResponse<?> uploadResponse = uploadDocument(datasetId, csvContent.getBytes("UTF-8"), fileName);
            
            if (uploadResponse == null || uploadResponse.getCode() == null || uploadResponse.getCode() != 0) {
                log.error("上传QA CSV文件失败: {}", uploadResponse);
                throw new RuntimeException("上传QA CSV文件失败");
            }
            
            // 3. 从上传响应中提取文档ID
            String documentId = extractDocumentIdFromUploadResponse(uploadResponse);
            if (documentId == null) {
                log.error("无法从上传响应中提取文档ID");
                throw new RuntimeException("无法获取上传文档的ID");
            }
            
            // 4. 更新文档配置，设置chunk_method为"qa"
            UpdateDocumentRequest updateRequest = new UpdateDocumentRequest();
            updateRequest.setChunk_method("qa");
            
            ApiResp<?> updateResponse = updateDocument(datasetId, documentId, updateRequest);
            if (updateResponse == null || updateResponse.getCode() == null || updateResponse.getCode() != 0) {
                log.error("更新文档chunk_method失败: {}", updateResponse);
                throw new RuntimeException("更新文档配置失败");
            }
            
            // 5. 解析文档
            ParseDocumentsRequest parseRequest = new ParseDocumentsRequest();
            parseRequest.setDocument_ids(Arrays.asList(documentId));
            
            RagFlowResponse<?> parseResponse = parseDocuments(datasetId, parseRequest);
            if (parseResponse == null || parseResponse.getCode() == null || parseResponse.getCode() != 0) {
                log.error("解析文档失败: {}", parseResponse);
                throw new RuntimeException("解析文档失败");
            }
            
            // 6. 等待解析完成后查询分段，获取ID
            List<QAUploadResponse> uploadedQAs = new ArrayList<>();
            try {
                // 稍等一下让解析完成
                Thread.sleep(2000);
                
                // 查询文档的分段
                Map<String, Object> params = new HashMap<>();
                params.put("page", 1);
                params.put("page_size", 1000);
                
                RagFlowResponse<?> chunksResponse = listChunks(datasetId, documentId, params);
                
                if (chunksResponse != null && chunksResponse.getCode() != null && chunksResponse.getCode() == 0) {
                    Object responseData = chunksResponse.getData();
                    
                    if (responseData instanceof FilteredChunkListResponse) {
                        FilteredChunkListResponse chunkListResponse = (FilteredChunkListResponse) responseData;
                        List<FilteredChunkResponse> chunks = chunkListResponse.getChunks();
                        
                        // 匹配QA对
                        for (QAUploadRequest originalQA : qaList) {
                            for (FilteredChunkResponse chunk : chunks) {
                                if (chunk.getContent() != null && 
                                    chunk.getContent().contains(originalQA.getQuestion()) && 
                                    chunk.getContent().contains(originalQA.getAnswer())) {
                                    
                                    QAUploadResponse uploadedQA = new QAUploadResponse();
                                    uploadedQA.setId(chunk.getId());
                                    uploadedQA.setQuestion(originalQA.getQuestion());
                                    uploadedQA.setAnswer(originalQA.getAnswer());
                                    uploadedQAs.add(uploadedQA);
                                    break;
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("查询新创建的QA分段失败，返回基本信息: {}", e.getMessage());
                // 如果查询失败，至少返回基本信息（不包含ID）
                for (QAUploadRequest qa : qaList) {
                    QAUploadResponse uploadedQA = new QAUploadResponse();
                    uploadedQA.setQuestion(qa.getQuestion());
                    uploadedQA.setAnswer(qa.getAnswer());
                    uploadedQAs.add(uploadedQA);
                }
            }
            
            log.info("新QA文档创建和处理完成，知识库ID: {}, 文档ID: {}, QA数量: {}", datasetId, documentId, uploadedQAs.size());
            return uploadedQAs;
            
        } catch (Exception e) {
            log.error("创建新QA文档时发生异常，知识库ID: {}", datasetId, e);
            throw new RuntimeException("创建QA文档时发生异常: " + e.getMessage(), e);
        }
    }

    /**
     * 从分段创建响应中提取chunk ID
     */
    private String extractChunkIdFromResponse(RagFlowResponse<?> response) {
        try {
            Object data = response.getData();
            
            // 处理FilteredChunkResponse对象
            if (data instanceof FilteredChunkResponse) {
                FilteredChunkResponse chunk = (FilteredChunkResponse) data;
                return chunk.getId();
            }
            // 处理原始Map格式（可能有嵌套结构）
            else if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                
                // 检查是否有嵌套的chunk结构 (data.chunk.id)
                if (dataMap.containsKey("chunk") && dataMap.get("chunk") instanceof Map) {
                    Map<String, Object> chunkMap = (Map<String, Object>) dataMap.get("chunk");
                    String chunkId = (String) chunkMap.get("id");
                    log.debug("从嵌套chunk结构中提取到ID: {}", chunkId);
                    return chunkId;
                }
                // 直接从顶层获取id（兼容性）
                else if (dataMap.containsKey("id")) {
                    String directId = (String) dataMap.get("id");
                    log.debug("从顶层结构中提取到ID: {}", directId);
                    return directId;
                }
            }
            
            log.warn("无法识别的分段创建响应数据格式: {}", data != null ? data.getClass().getSimpleName() : "null");
        } catch (Exception e) {
            log.error("提取分段ID失败: {}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public QAQueryListResponse queryQAPairs(List<String> datasetIds) {
        log.info("查询QA对，知识库ID列表: {}", datasetIds);
        
        if (datasetIds == null || datasetIds.isEmpty()) {
            log.warn("知识库ID列表为空");
            return new QAQueryListResponse(new ArrayList<>(), 0, 0);
        }
        
        List<QAQueryResponse> allDatasetResults = new ArrayList<>();
        int totalQAPairs = 0;
        
        for (String datasetId : datasetIds) {
            try {
                log.debug("开始查询知识库 {} 的QA对", datasetId);
                
                // 1. 从数据库查询该知识库下parser_id为"qa"的文档
                List<DocumentEntity> qaDocuments = documentRepository.findByKbIdAndParserId(datasetId, "qa");
                
                if (qaDocuments.isEmpty()) {
                    log.debug("知识库 {} 中未找到QA文档", datasetId);
                    // 创建空的结果
                    QAQueryResponse emptyResult = new QAQueryResponse();
                    emptyResult.setDataset_id(datasetId);
                    emptyResult.setQa_document_id(null);
                    emptyResult.setQa_pairs(new ArrayList<>());
                    emptyResult.setTotal_count(0);
                    allDatasetResults.add(emptyResult);
                    continue;
                }
                
                // 2. 取第一个QA文档（通常每个知识库只有一个QA文档）
                DocumentEntity qaDocument = qaDocuments.get(0);
                String qaDocumentId = qaDocument.getId();
                
                log.debug("找到QA文档，知识库ID: {}, QA文档ID: {}", datasetId, qaDocumentId);
                
                // 3. 调用分段查询接口获取QA对
                Map<String, Object> params = new HashMap<>();
                params.put("page", 1);
                params.put("page_size", 1000); // 设置较大的页面大小以获取所有QA对
                
                RagFlowResponse<?> chunksResponse = listChunks(datasetId, qaDocumentId, params);
                
                List<FilteredChunkResponse> qaPairs = new ArrayList<>();
                int count = 0;
                
                if (chunksResponse != null && chunksResponse.getCode() != null && chunksResponse.getCode() == 0) {
                    Object responseData = chunksResponse.getData();
                    
                    if (responseData instanceof FilteredChunkListResponse) {
                        FilteredChunkListResponse chunkListResponse = (FilteredChunkListResponse) responseData;
                        qaPairs = chunkListResponse.getChunks();
                        count = chunkListResponse.getTotal();
                        log.debug("从知识库 {} 获取到 {} 个QA对", datasetId, count);
                    } else {
                        log.warn("知识库 {} 的分段查询响应格式不正确: {}", datasetId, responseData != null ? responseData.getClass().getSimpleName() : "null");
                    }
                } else {
                    log.warn("查询知识库 {} 的分段失败: {}", datasetId, chunksResponse);
                }
                
                // 4. 创建该知识库的QA查询结果
                QAQueryResponse datasetResult = new QAQueryResponse();
                datasetResult.setDataset_id(datasetId);
                datasetResult.setQa_document_id(qaDocumentId);
                datasetResult.setQa_pairs(qaPairs);
                datasetResult.setTotal_count(count);
                
                allDatasetResults.add(datasetResult);
                totalQAPairs += count;
                
            } catch (Exception e) {
                log.error("查询知识库 {} 的QA对时发生异常", datasetId, e);
                
                // 创建错误结果
                QAQueryResponse errorResult = new QAQueryResponse();
                errorResult.setDataset_id(datasetId);
                errorResult.setQa_document_id(null);
                errorResult.setQa_pairs(new ArrayList<>());
                errorResult.setTotal_count(0);
                allDatasetResults.add(errorResult);
            }
        }
        
        log.info("QA对查询完成，总知识库数: {}, 总QA对数: {}", datasetIds.size(), totalQAPairs);
        
        return new QAQueryListResponse(allDatasetResults, datasetIds.size(), totalQAPairs);
    }

    @Override
    public QAPairListResponse queryQAPairsSimplified(List<String> datasetIds) {
        log.info("查询QA对（简化格式），知识库ID列表: {}", datasetIds);
        
        if (datasetIds == null || datasetIds.isEmpty()) {
            log.warn("知识库ID列表为空");
            return new QAPairListResponse(0, new ArrayList<>());
        }
        
        List<QAPairResponse> allQAPairs = new ArrayList<>();
        
        for (String datasetId : datasetIds) {
            try {
                log.debug("开始查询知识库 {} 的QA对", datasetId);
                
                // 1. 从数据库查询该知识库下parser_id为"qa"的文档
                List<DocumentEntity> qaDocuments = documentRepository.findByKbIdAndParserId(datasetId, "qa");
                
                if (qaDocuments.isEmpty()) {
                    log.debug("知识库 {} 中未找到QA文档", datasetId);
                    continue;
                }
                
                // 2. 处理每个QA文档
                for (DocumentEntity qaDocument : qaDocuments) {
                    String qaDocumentId = qaDocument.getId();
                    
                    log.debug("找到QA文档，知识库ID: {}, QA文档ID: {}", datasetId, qaDocumentId);
                    
                    // 3. 调用分段查询接口获取QA对
                    Map<String, Object> params = new HashMap<>();
                    params.put("page", 1);
                    params.put("page_size", 1000); // 设置较大的页面大小以获取所有QA对
                    
                    RagFlowResponse<?> chunksResponse = listChunks(datasetId, qaDocumentId, params);
                    
                    if (chunksResponse != null && chunksResponse.getCode() != null && chunksResponse.getCode() == 0) {
                        Object responseData = chunksResponse.getData();
                        
                        if (responseData instanceof FilteredChunkListResponse) {
                            FilteredChunkListResponse chunkListResponse = (FilteredChunkListResponse) responseData;
                            List<FilteredChunkResponse> chunks = chunkListResponse.getChunks();
                            
                            // 4. 转换每个chunk为QAPairResponse，传入datasetId
                            for (FilteredChunkResponse chunk : chunks) {
                                QAPairResponse qaPair = convertChunkToQAPair(chunk, datasetId);
                                if (qaPair != null) {
                                    allQAPairs.add(qaPair);
                                }
                            }
                            
                            log.debug("从知识库 {} 获取到 {} 个QA对", datasetId, chunks.size());
                        } else {
                            log.warn("知识库 {} 的分段查询响应格式不正确: {}", datasetId, responseData != null ? responseData.getClass().getSimpleName() : "null");
                        }
                    } else {
                        log.warn("查询知识库 {} 的分段失败: {}", datasetId, chunksResponse);
                    }
                }
                
            } catch (Exception e) {
                log.error("查询知识库 {} 的QA对时发生异常", datasetId, e);
            }
        }
        
        log.info("QA对查询完成（简化格式），总QA对数: {}", allQAPairs.size());
        
        return new QAPairListResponse(allQAPairs.size(), allQAPairs);
    }
    /**
     * 将FilteredChunkResponse转换为QAPairResponse
     * 解析content字段中的Question和Answer，支持多种格式
     */
    private QAPairResponse convertChunkToQAPair(FilteredChunkResponse chunk, String datasetId) {
        try {
            String content = chunk.getContent();
            if (content == null || content.trim().isEmpty()) {
                log.warn("QA对内容为空，ID: {}", chunk.getId());
                return null;
            }
            
            // 清理HTML标签
            String cleanContent = removeHtmlTags(content);
            
            String question = "";
            String answer = "";
            
            // 尝试多种格式解析
            boolean parsed = false;
            
            // 格式1: "Question: xxx\tAnswer: yyy" (标准格式) 或 "问题：xxx\t回答：yyy"
            if (!parsed && cleanContent.contains("\t")) {
                String[] parts = cleanContent.split("\t", 2);
                if (parts.length == 2) {
                    String questionPart = parts[0].trim();
                    String answerPart = parts[1].trim();
                    
                    if (questionPart.toLowerCase().startsWith("question:")) {
                        question = questionPart.substring(questionPart.indexOf(":") + 1).trim();
                        if (answerPart.toLowerCase().startsWith("answer:")) {
                            answer = answerPart.substring(answerPart.indexOf(":") + 1).trim();
                            parsed = true;
                        }
                    } else if (questionPart.toLowerCase().startsWith("问题：")) {
                        question = questionPart.substring(questionPart.indexOf("：") + 1).trim();
                        if (answerPart.toLowerCase().startsWith("回答：")) {
                            answer = answerPart.substring(answerPart.indexOf("：") + 1).trim();
                            parsed = true;
                        }
                    }

                }
            }
            // 格式2: "question:xxx answer:yyy" (空格分隔)
            if (!parsed) {
                // 使用正则表达式匹配 question:...answer:... 格式
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                    "question\\s*:\\s*(.*?)\\s+answer\\s*:\\s*(.*)",
                    java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
                );
                java.util.regex.Matcher matcher = pattern.matcher(cleanContent);
                if (matcher.find()) {
                    question = matcher.group(1).trim();
                    answer = matcher.group(2).trim();
                    parsed = true;
                }
            }
            // 格式3: "问题：xxx 回答：yyy" (空格分隔)
            if (!parsed) {
                // 使用正则表达式匹配 问题：...回答：... 格式
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                        "问题\\s*：\\s*(.*?)\\s+回答\\s*：\\s*(.*)",
                        java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
                );
                java.util.regex.Matcher matcher = pattern.matcher(cleanContent);
                if (matcher.find()) {
                    question = matcher.group(1).trim();
                    answer = matcher.group(2).trim();
                    parsed = true;
                }
            }
            
            // 格式4: "question{内容}answer{内容}" (直接拼接格式)
            if (!parsed) {
                // 使用正则表达式匹配 question...answer... 格式（无分隔符）
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                    "question(.*?)answer(.*)", 
                    java.util.regex.Pattern.CASE_INSENSITIVE | java.util.regex.Pattern.DOTALL
                );
                java.util.regex.Matcher matcher = pattern.matcher(cleanContent);
                if (matcher.find()) {
                    question = matcher.group(1).trim();
                    answer = matcher.group(2).trim();
                    parsed = true;
                }
            }
            
            // 格式5: 尝试其他可能的分隔符
            if (!parsed) {
                // 尝试换行符分隔
                String[] lines = cleanContent.split("\\r?\\n");
                if (lines.length >= 2) {
                    for (int i = 0; i < lines.length - 1; i++) {
                        String line1 = lines[i].trim();
                        String line2 = lines[i + 1].trim();
                        
                        if (line1.toLowerCase().startsWith("question:") && line2.toLowerCase().startsWith("answer:")) {
                            question = line1.substring(line1.indexOf(":") + 1).trim();
                            answer = line2.substring(line2.indexOf(":") + 1).trim();
                            parsed = true;
                            break;
                        }
                    }
                }
            }

            if (!parsed && !content.isEmpty()) {
                log.warn("QA对内容格式异常，ID: {}, content: {}", chunk.getId(), content);
                question = content;
                answer = "";
                parsed = true;
            }
            
            // 如果所有格式都解析失败，记录警告并返回null
            if (!parsed || question.isEmpty() || answer.isEmpty()) {
                log.warn("QA对内容格式无法解析，ID: {}, content: {}", chunk.getId(), content);
                return null;
            }
            
            QAPairResponse qaPair = new QAPairResponse();
            qaPair.setId(chunk.getId());
            qaPair.setDataset_id(datasetId);
            qaPair.setQuestion(question);
            qaPair.setAnswer(answer);
            qaPair.setAvailable(chunk.getAvailable());
            
            return qaPair;
            
        } catch (Exception e) {
            log.error("转换QA对时发生异常，ID: {}", chunk.getId(), e);
            return null;
        }
    }

    /**
     * 移除HTML标签
     */
    private String removeHtmlTags(String content) {
        if (content == null) {
            return null;
        }
        // 移除HTML标签，但保留文本内容
        return content.replaceAll("<[^>]+>", "");
    }

    @Override
    public RagFlowResponse<?> deleteQAPairs(String datasetId, List<String> qaIds) {
        log.info("删除QA对，知识库ID: {}, QA对IDs: {}", datasetId, qaIds);
        
        // 参数验证
        if (qaIds == null || qaIds.isEmpty()) {
            log.error("删除QA对失败：QA对ID列表为空，知识库ID: {}", datasetId);
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("QA对ID列表不能为空");
            errorResponse.setData(null);
            return errorResponse;
        }
        
        try {
            // 1. 查找QA文档ID
            String qaDocumentId = findExistingQADocument(datasetId);
            if (qaDocumentId == null) {
                log.warn("知识库 {} 中未找到QA文档，无法删除QA对", datasetId);
                RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
                errorResponse.setCode(-1);
                errorResponse.setMessage("知识库中未找到QA文档");
                errorResponse.setData(null);
                return errorResponse;
            }
            
            log.info("找到QA文档，知识库ID: {}, QA文档ID: {}", datasetId, qaDocumentId);
            
            // 2. 构建删除分段请求
            DeleteChunksRequest deleteRequest = new DeleteChunksRequest();
            deleteRequest.setChunk_ids(qaIds);
            
            // 3. 调用删除分段接口
            RagFlowResponse<?> response = deleteChunks(datasetId, qaDocumentId, deleteRequest);
            
            if (response != null && response.getCode() != null && response.getCode() == 0) {
                log.info("QA对删除成功，知识库ID: {}, 删除数量: {}", datasetId, qaIds.size());
            } else {
                log.warn("QA对删除失败，知识库ID: {}, 响应: {}", datasetId, response);
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("删除QA对时发生异常，知识库ID: {}, QA对IDs: {}", datasetId, qaIds, e);
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("删除QA对时发生异常: " + e.getMessage());
            errorResponse.setData(null);
            return errorResponse;
        }
    }

    @Override
    public RagFlowResponse<?> updateQAPair(String datasetId, String qaId, String question, String answer) {
        log.info("更新QA对，知识库ID: {}, QA对ID: {}, 问题: {}, 答案: {}", datasetId, qaId, question, answer);
        
        // 参数验证
        if (qaId == null || qaId.trim().isEmpty()) {
            log.error("更新QA对失败：QA对ID为空，知识库ID: {}", datasetId);
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("QA对ID不能为空");
            errorResponse.setData(null);
            return errorResponse;
        }
        
        if (question == null || question.trim().isEmpty()) {
            log.error("更新QA对失败：问题为空，知识库ID: {}, QA对ID: {}", datasetId, qaId);
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("问题不能为空");
            errorResponse.setData(null);
            return errorResponse;
        }
        
        if (answer == null || answer.trim().isEmpty()) {
            log.error("更新QA对失败：答案为空，知识库ID: {}, QA对ID: {}", datasetId, qaId);
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("答案不能为空");
            errorResponse.setData(null);
            return errorResponse;
        }
        
        try {
            // 1. 查找QA文档ID
            String qaDocumentId = findExistingQADocument(datasetId);
            if (qaDocumentId == null) {
                log.warn("知识库 {} 中未找到QA文档，无法更新QA对", datasetId);
                RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
                errorResponse.setCode(-1);
                errorResponse.setMessage("知识库中未找到QA文档");
                errorResponse.setData(null);
                return errorResponse;
            }
            
            log.info("找到QA文档，知识库ID: {}, QA文档ID: {}", datasetId, qaDocumentId);
            
            // 2. 构建更新分段请求，将QA对格式化为"Question: xxx\tAnswer: xxx"
            UpdateChunkRequest updateRequest = new UpdateChunkRequest();
            String content = "Question: " + question.trim() + "\tAnswer: " + answer.trim();
            updateRequest.setContent(content);
            
            // 3. 调用更新分段接口，使用qaId作为chunkId
            RagFlowResponse<?> response = updateChunkInDocument(datasetId, qaDocumentId, qaId, updateRequest);
            
            if (response != null && response.getCode() != null && response.getCode() == 0) {
                log.info("QA对更新成功，知识库ID: {}, QA对ID: {}", datasetId, qaId);
            } else {
                log.warn("QA对更新失败，知识库ID: {}, QA对ID: {}, 响应: {}", datasetId, qaId, response);
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("更新QA对时发生异常，知识库ID: {}, QA对ID: {}", datasetId, qaId, e);
            RagFlowResponse<Object> errorResponse = new RagFlowResponse<>();
            errorResponse.setCode(-1);
            errorResponse.setMessage("更新QA对时发生异常: " + e.getMessage());
            errorResponse.setData(null);
            return errorResponse;
        }
    }

    @Override
    public QAPairListResponse queryQAPairsWithSearch(List<String> datasetIds, String keywords, Integer page, Integer pageSize) {
        log.info("查询QA对（支持搜索），知识库ID列表: {}, 关键词: {}, 页码: {}, 每页条数: {}", 
                 datasetIds, keywords, page, pageSize);
        
        if (datasetIds == null || datasetIds.isEmpty()) {
            log.warn("知识库ID列表为空");
            return null;
        }
        
        // 设置默认值
        int currentPage = page != null ? Math.max(1, page) : 1;
        int currentPageSize = pageSize != null ? Math.max(1, Math.min(pageSize, 1000)) : 50;
        
        List<QAPairResponse> allQAPairs = new ArrayList<>();
        
        for (String datasetId : datasetIds) {
            try {
                log.debug("开始查询知识库 {} 的QA对", datasetId);
                
                // 1. 从数据库查询该知识库下parser_id为"qa"的文档
                List<DocumentEntity> qaDocuments = documentRepository.findByKbIdAndParserId(datasetId, "qa");
                
                if (qaDocuments.isEmpty()) {
                    log.debug("知识库 {} 中未找到QA文档", datasetId);
                    continue;
                }
                
                // 2. 处理每个QA文档
                for (DocumentEntity qaDocument : qaDocuments) {
                    String qaDocumentId = qaDocument.getId();
                    
                    log.debug("找到QA文档，知识库ID: {}, QA文档ID: {}", datasetId, qaDocumentId);
                    
                    // 3. 调用分段查询接口获取QA对，支持关键词搜索
                    Map<String, Object> params = new HashMap<>();
                    params.put("page", 1);
                    params.put("page_size", 1000); // 设置较大的页面大小以获取所有QA对
                    
                    // 如果有关键词，添加到查询参数
                    if (keywords != null && !keywords.trim().isEmpty()) {
                        params.put("keywords", keywords.trim());
                    }
                    
                    RagFlowResponse<?> chunksResponse = listChunks(datasetId, qaDocumentId, params);
                    
                    if (chunksResponse != null && chunksResponse.getCode() != null && chunksResponse.getCode() == 0) {
                        Object responseData = chunksResponse.getData();
                        
                        if (responseData instanceof FilteredChunkListResponse) {
                            FilteredChunkListResponse chunkListResponse = (FilteredChunkListResponse) responseData;
                            List<FilteredChunkResponse> chunks = chunkListResponse.getChunks();
                            
                            // 4. 转换每个chunk为QAPairResponse
                            for (FilteredChunkResponse chunk : chunks) {
                                QAPairResponse qaPair = convertChunkToQAPair(chunk, datasetId);
                                if (qaPair != null) {
                                    // 如果有关键词，进行额外的内容过滤（双重保险）
                                    if (keywords == null || keywords.trim().isEmpty() || 
                                        matchesKeywords(qaPair, keywords.trim())) {
                                        allQAPairs.add(qaPair);
                                    }
                                }
                            }
                            
                            log.debug("从知识库 {} 获取到 {} 个QA对", datasetId, chunks.size());
                        } else {
                            log.warn("知识库 {} 的分段查询响应格式不正确: {}", datasetId, 
                                    responseData != null ? responseData.getClass().getSimpleName() : "null");
                        }
                    } else {
                        log.warn("查询知识库 {} 的分段失败: {}", datasetId, chunksResponse);
                    }
                }
                
            } catch (Exception e) {
                log.error("查询知识库 {} 的QA对时发生异常", datasetId, e);
            }
        }
        
        // 5. 实现分页逻辑
        int totalQAPairs = allQAPairs.size();
        int totalPages = (int) Math.ceil((double) totalQAPairs / currentPageSize);
        
        // 计算分页起始和结束位置
        int startIndex = (currentPage - 1) * currentPageSize;
        int endIndex = Math.min(startIndex + currentPageSize, totalQAPairs);
        
        List<QAPairResponse> pagedQAPairs = new ArrayList<>();
        if (startIndex < totalQAPairs) {
            pagedQAPairs = allQAPairs.subList(startIndex, endIndex);
        }
        
        log.info("QA对查询完成（支持搜索），总QA对数: {}, 当前页: {}, 每页条数: {}, 返回条数: {}", 
                 totalQAPairs, currentPage, currentPageSize, pagedQAPairs.size());
        
//        return new QAPairListResponse(totalQAPairs, pagedQAPairs, currentPage, currentPageSize, totalPages);
        return new QAPairListResponse(totalQAPairs, pagedQAPairs);
    }

    /**
     * 检查QA对是否匹配搜索关键词
     */
    private boolean matchesKeywords(QAPairResponse qaPair, String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return true;
        }
        
        String keywordsLower = keywords.toLowerCase();
        
        // 检查问题中是否包含关键词
        if (qaPair.getQuestion() != null && qaPair.getQuestion().toLowerCase().contains(keywordsLower)) {
            return true;
        }
        
        // 检查答案中是否包含关键词
        if (qaPair.getAnswer() != null && qaPair.getAnswer().toLowerCase().contains(keywordsLower)) {
            return true;
        }
        
        return false;
    }

    /**
     * 更新新文档的parser_id和parser_config字段，使用原文档的数据
     * @param sourceDocId 原文档ID
     * @param targetDocId 目标文档ID（新文档）
     */
    private void updateDocumentParserConfig(String sourceDocId, String targetDocId) {
        try {
            // 1. 查询原文档的parser_id和parser_config
            DocumentEntity sourceDocument = documentRepository.findById(sourceDocId)
                    .orElse(null);
            
            if (sourceDocument == null) {
                log.warn("未找到原文档，无法复制parser配置，原文档ID: {}", sourceDocId);
                return;
            }
            
            String originalParserId = sourceDocument.getParserId();
            String originalParserConfig = sourceDocument.getParserConfig();
            
            // 调用重载方法
            updateDocumentParserFields(targetDocId, originalParserId, originalParserConfig);
            
        } catch (Exception e) {
            log.error("更新文档parser配置失败，原文档[{}] -> 目标文档[{}], 错误: {}",
                    sourceDocId, targetDocId, e.getMessage(), e);
        }
    }

    /**
     * 直接更新文档的parser_id和parser_config字段
     * @param documentId 文档ID
     * @param parserId 解析器ID
     * @param parserConfig 解析器配置
     */
    public void updateDocumentParserFields(String documentId, String parserId, String parserConfig) {
        try {
            // 查询目标文档
            DocumentEntity targetDocument = documentRepository.findById(documentId)
                    .orElse(null);

            // 更新目标文档的parser_id和parser_config
            targetDocument.setParserId(parserId);
            targetDocument.setParserConfig(parserConfig);
            
            // 保存到数据库
            documentRepository.save(targetDocument);
            
            log.info("成功更新文档parser配置，文档[{}], parser_id: {}", documentId, parserId);
            
        } catch (Exception e) {
            log.error("更新文档parser配置失败，文档[{}], 错误: {}", documentId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public ApiResp<?> copyDialogConfig(String sourceDialogId, List<String> targetDialogIds) {
        log.info("复制对话配置，源对话ID: {}, 目标对话ID列表: {}", sourceDialogId, targetDialogIds);

        if (sourceDialogId == null || sourceDialogId.trim().isEmpty()) {
            log.error("源对话ID不能为空");
            return ApiResp.error(400, "源对话ID不能为空");
        }

        if (targetDialogIds == null || targetDialogIds.isEmpty()) {
            log.error("目标对话ID列表不能为空");
            return ApiResp.error(400, "目标对话ID列表不能为空");
        }

        try {
            // 1. 查询源对话记录
            DialogEntity sourceDialog = dialogRepository.findById(sourceDialogId).orElse(null);
            if (sourceDialog == null) {
                log.error("未找到源对话记录，源对话ID: {}", sourceDialogId);
                return ApiResp.error(404, "未找到源对话记录");
            }

            String sourcePromptConfig = sourceDialog.getPromptConfig();
            if (sourcePromptConfig == null || sourcePromptConfig.trim().isEmpty()) {
                log.error("源对话记录的prompt_config为空，源对话ID: {}", sourceDialogId);
                return ApiResp.error(400, "源对话记录的prompt_config为空");
            }

            // 获取源对话的所有配置参数
            Float sourceSimilarityThreshold = sourceDialog.getSimilarityThreshold();
            Float sourceVectorSimilarityWeight = sourceDialog.getVectorSimilarityWeight();
            Integer sourceTopN = sourceDialog.getTopN();
            Integer sourceTopK = sourceDialog.getTopK();
            String sourceRerankId = sourceDialog.getRerankId();

            log.info("获取到源对话配置，源对话ID: {}, prompt_config长度: {}, similarity_threshold: {}, vector_similarity_weight: {}, top_n: {}, top_k: {}, rerank_id: {}", 
                    sourceDialogId, sourcePromptConfig.length(), sourceSimilarityThreshold, sourceVectorSimilarityWeight, sourceTopN, sourceTopK, sourceRerankId);

            // 2. 批量复制配置到目标对话记录
            int copiedCount = 0;
            int skippedCount = 0;
            int notFoundCount = 0;

            for (String targetDialogId : targetDialogIds) {
                try {
                    // 排除源对话记录本身
                    if (sourceDialogId.equals(targetDialogId)) {
                        log.debug("跳过源对话记录本身，对话ID: {}", sourceDialogId);
                        skippedCount++;
                        continue;
                    }

                    // 检查目标对话记录是否存在
                    DialogEntity targetDialog = dialogRepository.findById(targetDialogId).orElse(null);
                    if (targetDialog == null) {
                        log.warn("目标对话记录不存在，对话ID: {}", targetDialogId);
                        notFoundCount++;
                        continue;
                    }

                    // 复制所有配置字段
                    targetDialog.setPromptConfig(sourcePromptConfig);
                    targetDialog.setSimilarityThreshold(sourceSimilarityThreshold);
                    targetDialog.setVectorSimilarityWeight(sourceVectorSimilarityWeight);
                    targetDialog.setTopN(sourceTopN);
                    targetDialog.setTopK(sourceTopK);
                    targetDialog.setRerankId(sourceRerankId);
                    
                    // 保存更新
                    dialogRepository.save(targetDialog);
                    copiedCount++;

                    log.debug("成功复制所有配置到对话[{}] - prompt_config, similarity_threshold: {}, vector_similarity_weight: {}, top_n: {}, top_k: {}, rerank_id: {}", 
                             targetDialogId, sourceSimilarityThreshold, sourceVectorSimilarityWeight, sourceTopN, sourceTopK, sourceRerankId);

                } catch (Exception e) {
                    log.error("复制配置到对话[{}]时发生异常: {}", targetDialogId, e.getMessage(), e);
                    // 继续处理其他记录，不因单个记录失败而中断整个复制操作
                }
            }

            String resultMessage = String.format("对话配置复制完成 - 成功: %d, 跳过: %d, 不存在: %d (包含prompt_config、similarity_threshold、vector_similarity_weight、top_n、top_k、rerank_id)", 
                                                copiedCount, skippedCount, notFoundCount);
            log.info("复制对话配置完成，{}", resultMessage);
            
            return ApiResp.success(resultMessage, copiedCount);

        } catch (Exception e) {
            log.error("复制对话配置时发生异常: {}", e.getMessage(), e);
            return ApiResp.error(500, "复制失败: " + e.getMessage());
        }
    }

} 