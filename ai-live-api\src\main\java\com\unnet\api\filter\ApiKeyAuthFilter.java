package com.unnet.api.filter;

import com.unnet.api.config.AppProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@Order(1)
@RequiredArgsConstructor
@Slf4j
public class ApiKeyAuthFilter extends OncePerRequestFilter {

    private static final String API_KEY_HEADER = "X-API-KEY";
    private static final String CLIENT_ID_ATTRIBUTE = "clientId";
    
    private final AppProperties appProperties;
    private final ObjectMapper objectMapper;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) 
            throws ServletException, IOException {
        
        String requestUri = request.getRequestURI();
        log.info("requestUri:{}", requestUri);
        // 如果路径不需要验证API Key，则直接放行
        if (!requiresAuthentication(requestUri)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        String apiKey = request.getHeader(API_KEY_HEADER);
        
        // 如果请求中没有API Key，则返回401错误
        if (apiKey == null || apiKey.isEmpty()) {
            handleUnauthorized(response, "缺少API Key");
            return;
        }
        
        // 验证API Key是否有效
        Optional<AppProperties.Api.ApiKey> validApiKey = appProperties.getApi().getKeys().stream()
                .filter(k -> k.getKey().equals(apiKey))
                .findFirst();
        
        if (validApiKey.isPresent()) {
            // API Key有效，将客户端ID存入请求属性，供后续业务逻辑使用
            request.setAttribute(CLIENT_ID_ATTRIBUTE, validApiKey.get().getClientId());
            filterChain.doFilter(request, response);
        } else {
            // API Key无效，返回401错误
            handleUnauthorized(response, "无效的API Key");
        }
    }
    
    /**
     * 判断请求路径是否需要API Key认证
     */
    private boolean requiresAuthentication(String requestUri) {
        // 这里可以配置不需要验证API Key的路径
        String[] whiteListedPaths = {
            "/aiLive/api/v1/tts/api/download_tip_*",    //提示音频、文本下载免校验
            "/actuator/**",
            "/swagger-ui/**",
            "/swagger-ui.html",
            "/swagger-resources/**",
            "/v3/api-docs/**",
            "/v3/api-docs.yaml",
            "/aiLive/swagger-ui/**",
            "/aiLive/swagger-ui.html",
            "/aiLive/v3/api-docs/**",
            "/aiLive/v3/api-docs.yaml",
            "/aiLive/swagger-resources/**",
            "/error"
        };
        
        for (String path : whiteListedPaths) {
            if (pathMatcher.match(path, requestUri)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 处理未授权的请求
     */
    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        errorDetails.put("status", HttpStatus.UNAUTHORIZED.value());
        errorDetails.put("error", "Unauthorized");
        errorDetails.put("message", message);
        
        response.getWriter().write(objectMapper.writeValueAsString(errorDetails));
    }
} 