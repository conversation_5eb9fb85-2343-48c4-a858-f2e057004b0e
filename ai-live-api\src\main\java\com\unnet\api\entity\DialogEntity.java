package com.unnet.api.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "dialog")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class DialogEntity {
    @Id
    @Column(name = "id", length = 32)
    private String id;

    @Column(name = "create_time")
    private Long createTime;

    @Column(name = "create_date")
    private LocalDateTime createDate;

    @Column(name = "update_time")
    private Long updateTime;

    @Column(name = "update_date")
    private LocalDateTime updateDate;

    @Column(name = "tenant_id", length = 32, nullable = false)
    private String tenantId;

    @Column(name = "name", length = 255)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "icon", columnDefinition = "TEXT")
    private String icon;

    @Column(name = "language", length = 32)
    private String language;

    @Column(name = "llm_id", length = 128, nullable = false)
    private String llmId;

    @Column(name = "llm_setting", columnDefinition = "LONGTEXT", nullable = false)
    private String llmSetting;

    @Column(name = "prompt_type", length = 16, nullable = false)
    private String promptType;

    @Column(name = "prompt_config", columnDefinition = "LONGTEXT", nullable = false)
    private String promptConfig;

    @Column(name = "similarity_threshold", nullable = false)
    private Float similarityThreshold;

    @Column(name = "vector_similarity_weight", nullable = false)
    private Float vectorSimilarityWeight;

    @Column(name = "top_n", nullable = false)
    private Integer topN;

    @Column(name = "top_k", nullable = false)
    private Integer topK;

    @Column(name = "do_refer", length = 1, nullable = false)
    private String doRefer;

    @Column(name = "rerank_id", length = 128, nullable = false)
    private String rerankId;

    @Column(name = "kb_ids", columnDefinition = "LONGTEXT", nullable = false)
    private String kbIds;

    @Column(name = "status", length = 1)
    private String status;

}
