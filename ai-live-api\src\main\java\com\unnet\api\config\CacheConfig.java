package com.unnet.api.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {

    @Autowired
    private AppProperties appProperties;

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("nonceCache");
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(appProperties.getSecurity().getReplayProtection().getNonceCacheMinutes(), TimeUnit.MINUTES)
                .maximumSize(10000));
        return cacheManager;
    }
} 