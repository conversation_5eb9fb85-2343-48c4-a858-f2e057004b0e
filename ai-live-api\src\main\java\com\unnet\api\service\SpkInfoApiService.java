package com.unnet.api.service;

import com.unnet.api.entity.Voice;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.util.Map;
import java.util.List;

public interface SpkInfoApiService {

    List<Map<String,String>> find(String userid);

    Map<String,String> upload(Voice voice) throws IOException;

    void delete(String spkName, String userid);

    // model=1 表示下载提示音频  model=2 表示下载试听音频
    ResponseEntity<byte[]> download(String spkName, String userid ,int model) throws Exception;

    String downloadTips(String spkName, String userid) throws Exception;
}
