package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新QA对请求")
public class QAUpdateRequest {

    @Schema(description = "问题", required = true, example = "比亚迪秦2022款的售价是多少？")
    private String question;

    @Schema(description = "答案", required = true, example = "大概11.6万")
    private String answer;
} 