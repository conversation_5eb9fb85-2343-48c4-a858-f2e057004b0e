---
description: 
globs: 
alwaysApply: false
---
# 项目概览

这是一个4S店AI直播平台大模型相关API服务，基于Spring Boot 2.7.x构建。

## 主要功能
- 提供AI直播平台的后端API服务
- 实现API Key认证机制
- 防重放攻击保护
- 参数校验和全局异常处理
- AOP日志记录
- 缓存支持

## 技术栈
- **框架**: Spring Boot 2.7.x
- **Web**: Spring MVC
- **验证**: Bean Validation  
- **切面**: Spring AOP
- **缓存**: Spring Cache + Caffeine
- **工具**: Lombok
- **日志**: SLF4J (Logback)
- **文档**: Swagger3 (OpenAPI)
- **构建**: Maven

## 关键文件
- 主应用类: [AiLiveApiApplication.java](mdc:src/main/java/com/unnet/api/AiLiveApiApplication.java)
- Maven配置: [pom.xml](mdc:pom.xml)
- 项目文档: [README.md](mdc:README.md)

## 端口和访问
- 默认端口: 8081
- Swagger文档: http://localhost:8081/api/swagger-ui/index.html
