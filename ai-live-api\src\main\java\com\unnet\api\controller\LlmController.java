package com.unnet.api.controller;

import com.unnet.api.dto.*;
import com.unnet.api.entity.KnowledgebaseEntity;
import com.unnet.api.service.ChatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;
import java.util.Arrays;

/**
 * LLM对话控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/llm")
@RequiredArgsConstructor
@Tag(name = "LLM对话", description = "大语言模型对话接口")
public class LlmController {

    private final ChatService chatService;

    /**
     * 通用聊天接口 - 支持流式和非流式 (原OpenAI兼容接口)
     */
    @PostMapping("chats/common")
    @Operation(summary = "OPENAI通用协议聊天接口（不检索知识库）", description = "发送消息给大语言模型，支持流式和非流式响应")
    public Object chat(@Valid @RequestBody ChatRequest request) {
        try {
            log.info("收到聊天请求，流式模式: {}", request.getStream());
            
            // 根据stream参数决定返回类型
            if (Boolean.TRUE.equals(request.getStream())) {
                // 真正的流式响应
                SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
                
                // 设置错误和完成处理
                emitter.onCompletion(() -> log.info("OpenAI兼容流式响应完成"));
                emitter.onError(throwable -> log.error("OpenAI兼容流式响应错误", throwable));
                emitter.onTimeout(() -> log.warn("OpenAI兼容流式响应超时"));
                
                // 在新线程中处理流式响应，避免阻塞主线程
                new Thread(() -> {
                    try {
                        chatService.callCommonLLMStream(request, emitter); // Note: This uses the direct LLM stream
                    } catch (Exception e) {
                        log.error("OpenAI兼容流式处理异常", e);
                        emitter.completeWithError(e);
                    }
                }).start();
                
                return emitter;
            } else {
                // 非流式响应
                ChatResponse response = chatService.processChat(request); // Note: This uses the direct LLM call
                return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response);
            }
        } catch (Exception e) {
            log.error("聊天请求处理失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 带会话管理的聊天接口 (新)
     * 如果未提供sessionId，则创建新会话。
     * 支持流式和非流式响应。
     */
    @PostMapping("/chats/completions")
    @Operation(summary = "脚本生成会话接口（会检索相关知识库）",
               description = "与指定的聊天助手进行对话。如果未提供sessionId，则自动创建新会话。")
    public Object liveScriptChat(@Valid @RequestBody CommonChatRequest request) {
        try {
            String datasetId = request.getDatasetId();
            String sessionIdFromRequest = request.getSessionId();
            String question = request.getQuestion();
            boolean stream = Boolean.TRUE.equals(request.getStream());

            log.info("收到脚本生成会话请求: datasetId={}, sessionId={}, stream={}",
                    datasetId, sessionIdFromRequest, stream);

            // 查询相关知识库
            KnowledgebaseEntity database;
            try {
                database = chatService.getKbByDatasetId(datasetId);
                if (null == database) {
                    log.warn("根据 datasetId={} 未找到对应的知识库", datasetId);
                    return ResponseEntity.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(ApiResp.error(400, "无效的 datasetId，未能找到对应的知识库"));
                }
            } catch (Exception e) {
                log.error("根据 datasetId={} 查询知识库失败: {}", datasetId, e.getMessage(), e);
                return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(ApiResp.error(400, "查询知识库失败: " + e.getMessage()));
            }

            // 查询 chatId
            String chatId;
            try {
                chatId = chatService.getChatIdByDatasetId(datasetId); 
                if (chatId == null || chatId.trim().isEmpty()) {
                    log.warn("根据 datasetId={} 未找到对应的 chatId", datasetId);
                    return ResponseEntity.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(ApiResp.error(400, "无效的 datasetId，未能找到对应的聊天助手配置"));
                }
                log.info("根据 datasetId={} 查询到 chatId={}", datasetId, chatId);
            } catch (Exception e) {
                log.error("根据 datasetId={} 查询 chatId 失败: {}", datasetId, e.getMessage(), e);
                return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(ApiResp.error(400, "查询聊天助手配置失败: " + e.getMessage()));
            }
            

            String currentSessionId = sessionIdFromRequest;
            if (currentSessionId == null || currentSessionId.trim().isEmpty()) {
                try {
                    log.info("sessionId未提供，正在为chatId={}创建新会话...", chatId);
                    currentSessionId = chatService.createChatSession(chatId, question);
                    log.info("新会话创建成功，sessionId={}，用于chatId={}", currentSessionId, chatId);
                } catch (Exception e) {
                    log.error("为chatId={}创建新会话失败: {}", chatId, e.getMessage(), e);
                    return ResponseEntity.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(ApiResp.error(400, "创建新会话失败: " + e.getMessage()));
                }
            }
            
            final String finalSessionIdToUse = currentSessionId;

            // 拼接SessionChatRequest对象
            SessionChatRequest chatRequest = new SessionChatRequest();
            chatRequest.setDatasetId(datasetId);
            chatRequest.setSessionId(currentSessionId);
            chatRequest.setQuestion(question);
            chatRequest.setStream(stream);
            chatRequest.setLiveStyle(chatService.getPresetScriptStyle());
            chatRequest.setContext(chatService.getPresetScriptContext(database.getDescription(), request.getExtraInfo()));

            if (stream) {
                SseEmitter emitter = new SseEmitter(300000L); // 5 分钟超时
                emitter.onCompletion(() -> log.info("脚本生成会话流式响应完成, sessionId={}", finalSessionIdToUse));
                emitter.onError(throwable -> log.error("脚本生成会话流式响应错误, sessionId={}", finalSessionIdToUse, throwable));
                emitter.onTimeout(() -> log.warn("脚本生成会话流式响应超时, sessionId={}", finalSessionIdToUse));

                new Thread(() -> {
                    try {
                        chatService.converseInSessionStream(chatRequest, chatId, finalSessionIdToUse, emitter);
                    } catch (Exception e) {
                        log.error("脚本生成会话流式处理异常, sessionId={}", finalSessionIdToUse, e);
                        emitter.completeWithError(e);
                    }
                }).start();
                return emitter;
            } else {
                SessionChatResponse response = chatService.converseInSession(chatRequest, chatId, finalSessionIdToUse);
                return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response);
            }

        } catch (Exception e) {
            log.error("脚本生成会话请求处理失败", e);
            // Avoid exposing raw exception messages to client if possible
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ApiResp.error(400, "聊天请求处理失败"));
        }
    }

    /**
     * 直播间弹幕问题回答接口
     */
    @PostMapping("/chats/comments")
    @Operation(summary = "直播间弹幕问题回答接口", 
               description = "根据用户弹幕问题、知识库ID和直播点位列表生成对应的直播脚本")
    public ResponseEntity<ApiResp<LiveCommentResponse>> answerLiveComment(@Valid @RequestBody LiveCommentRequest request) {

        // 20250626与客户确认直播点位列表固定为：车头、车身、车尾
        request.setLivePoints(Arrays.asList("车头", "车身", "车尾"));

        try {
            log.info("收到直播间弹幕问题请求: question={}, datasetId={}, livePoints={}", 
                    request.getQuestion(), request.getDatasetId(), request.getLivePoints());

            LiveCommentResponse response = chatService.processLiveComment(request);
            
            log.info("直播间弹幕问题处理成功: script={}, location={}", 
                    response.getScript(), response.getLocation());

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ApiResp.success(response));

        } catch (Exception e) {
            log.error("直播间弹幕问题处理失败: {}", e.getMessage(), e);
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ApiResp.error(400, "弹幕问题处理失败: " + e.getMessage()));
        }
    }

} 