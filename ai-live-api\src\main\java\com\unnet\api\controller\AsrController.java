package com.unnet.api.controller;

import com.unnet.api.dto.ApiResp;
import com.unnet.api.dto.AsrTaskCreateResponse;
import com.unnet.api.dto.AsrTaskStatusResponse;
import com.unnet.api.service.AsrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * ASR语音识别控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/asr")
@RequiredArgsConstructor
@Tag(name = "ASR语音识别", description = "自动语音识别服务接口")
public class AsrController {
    
    private final AsrService asrService;
    
    /**
     * 创建ASR识别任务
     */
    @PostMapping(value = "/recognize/async", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "创建ASR识别任务", description = "上传音频文件创建异步语音识别任务")
    public ResponseEntity<ApiResp<AsrTaskCreateResponse>> createAsrTask(
            @Parameter(description = "音频文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("收到ASR任务创建请求，文件名: {}", file.getOriginalFilename());
            
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.ok(ApiResp.error(400, "音频文件不能为空"));
            }
            
            // 验证文件类型（可选，根据需要添加音频格式检查）
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".wav") 
                    && !fileName.toLowerCase().endsWith(".mp3")
                    && !fileName.toLowerCase().endsWith(".m4a")
                    && !fileName.toLowerCase().endsWith(".flac"))) {
                return ResponseEntity.ok(ApiResp.error(400, "不支持的音频格式，请上传 wav、mp3、m4a 或 flac 格式文件"));
            }
            
            // 调用服务创建任务
            AsrTaskCreateResponse response = asrService.createAsrTask(file);
            
            if (response != null && response.getTaskId() != null) {
                log.info("ASR任务创建成功，任务ID: {}", response.getTaskId());
                return ResponseEntity.ok(ApiResp.success("任务创建成功", response));
            } else {
                log.warn("ASR任务创建失败");
                return ResponseEntity.ok(ApiResp.error(500, "任务创建失败"));
            }
            
        } catch (Exception e) {
            log.error("ASR任务创建请求处理失败", e);
            return ResponseEntity.ok(ApiResp.error(500, "任务创建失败: " + e.getMessage()));
        }
    }
    
    /**
     * 查询ASR任务状态
     */
    @GetMapping("/recognize/status/{taskId}")
    @Operation(summary = "查询ASR任务状态", description = "根据任务ID查询语音识别任务的处理状态和结果")
    public ResponseEntity<ApiResp<AsrTaskStatusResponse>> getTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        
        try {
            log.info("收到ASR任务状态查询请求，任务ID: {}", taskId);
            
            // 验证任务ID
            if (taskId == null || taskId.trim().isEmpty()) {
                return ResponseEntity.ok(ApiResp.error(400, "任务ID不能为空"));
            }
            
            // 调用服务查询状态
            AsrTaskStatusResponse response = asrService.getTaskStatus(taskId);
            
            if (response != null && response.getTaskId() != null) {
                log.info("ASR任务状态查询成功，任务ID: {}, 状态: {}", 
                        taskId, response.getStatus());
                return ResponseEntity.ok(ApiResp.success("查询成功", response));
            } else {
                log.warn("ASR任务状态查询失败，任务ID: {}", taskId);
                return ResponseEntity.ok(ApiResp.error(404, "任务不存在或查询失败"));
            }
            
        } catch (Exception e) {
            log.error("ASR任务状态查询请求处理失败，任务ID: {}", taskId, e);
            return ResponseEntity.ok(ApiResp.error(500, "状态查询失败: " + e.getMessage()));
        }
    }
} 