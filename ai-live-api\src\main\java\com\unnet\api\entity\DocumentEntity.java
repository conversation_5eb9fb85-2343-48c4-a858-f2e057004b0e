package com.unnet.api.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "document")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class DocumentEntity {

    @Id
    @Column(name = "id", length = 32, nullable = false)
    private String id; // 主键，对应 RagFlow 文档ID

    @Column(name = "create_time")
    private Long createTime;

    @Column(name = "create_date")
    private LocalDateTime createDate;

    @Column(name = "update_time")
    private Long updateTime;

    @Column(name = "update_date")
    private LocalDateTime updateDate;

    @Lob
    @Column(name = "thumbnail")
    private String thumbnail;

    @Column(name = "kb_id", length = 256, nullable = false)
    private String kbId; // 所属知识库ID

    @Column(name = "parser_id", length = 32, nullable = false)
    private String parserId;

    @Lob
    @Column(name = "parser_config", nullable = false)
    private String parserConfig;

    @Column(name = "source_type", length = 128, nullable = false)
    private String sourceType;

    @Column(name = "type", length = 32, nullable = false)
    private String type;

    @Column(name = "created_by", length = 32, nullable = false)
    private String createdBy;

    @Column(name = "name", length = 255)
    private String name; // 文档名称

    @Column(name = "location", length = 255)
    private String location;

    @Column(name = "size", nullable = false)
    private Integer size;

    @Column(name = "token_num", nullable = false)
    private Integer tokenNum;

    @Column(name = "chunk_num", nullable = false)
    private Integer chunkNum;

    @Column(name = "progress", nullable = false)
    private Float progress;

    @Lob
    @Column(name = "progress_msg")
    private String progressMsg;

    @Column(name = "process_begin_at")
    private LocalDateTime processBeginAt;

    @Column(name = "process_duation", nullable = false)
    private Float processDuation;

    @Lob
    @Column(name = "meta_fields")
    private String metaFields;

    @Column(name = "run", length = 1)
    private String run;

    @Column(name = "status", length = 1)
    private String status; // 文档状态，例如 "1" 代表启用, "0" 代表未启用
} 