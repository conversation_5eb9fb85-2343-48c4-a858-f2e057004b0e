package com.unnet.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "非流式响应，来自与带会话管理的聊天助手的对话")
public class SessionChatResponse {

    @Schema(description = "助手的回答")
    private String answer;

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "聊天助手ID")
    private String chatId;

    @Schema(description = "消息ID")
    private String messageId;
} 