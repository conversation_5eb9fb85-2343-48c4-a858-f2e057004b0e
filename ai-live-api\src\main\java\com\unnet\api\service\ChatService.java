package com.unnet.api.service;

import com.unnet.api.dto.ChatRequest;
import com.unnet.api.dto.ChatResponse;
import com.unnet.api.dto.LiveCommentRequest;
import com.unnet.api.dto.LiveCommentResponse;
import com.unnet.api.dto.SessionChatRequest;
import com.unnet.api.dto.SessionChatResponse;
import com.unnet.api.entity.KnowledgebaseEntity;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 *
 * <AUTHOR>
 */
public interface ChatService {

    /**
     * 调用真实的LLM模型（流式）
     */
    void callCommonLLMStream(ChatRequest request, SseEmitter emitter);

    /**
     * 处理通用聊天请求
     */
    ChatResponse processChat(ChatRequest request);

    String createChatSession(String chatId, String question) throws RuntimeException;

    void converseInSessionStream(SessionChatRequest request, String chatId, String actualSessionId, SseEmitter emitter);

    SessionChatResponse converseInSession(SessionChatRequest request, String chatId, String actualSessionId);


    /**
     * 根据知识库ID查询知识库信息
     * @param datasetId 知识库ID
     * @return 知识库
     */
    KnowledgebaseEntity getKbByDatasetId(String datasetId);

    /**
     * 根据知识库ID查询聊天助手ID
     * @param datasetId 知识库ID
     * @return 聊天助手ID
     */
    String getChatIdByDatasetId(String datasetId);

    /**
     * 处理直播间弹幕问题
     * @param request 直播间弹幕问题请求
     * @return 生成的脚本和定位信息
     */
    LiveCommentResponse processLiveComment(LiveCommentRequest request);

    /**
     * 构建直播间弹幕问题的特殊提示词
     */
    String getLiveCommentStyle();

    /**
     * 构建直播间弹幕问题的上下文
     */
    String getLiveCommentContext(String kbDescription, String userContext);

    /**
     * 构建预置脚本生成的特殊提示词
     */
    String getPresetScriptStyle();

    /**
     * 构建预置脚本生成的上下文
     */
    String getPresetScriptContext(String kbDescription, String userContext);
}
