package com.unnet.api.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;


import javax.validation.constraints.Size;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Voice {
    // 音频文件 (支持.wav或.mp3格式)
    @Schema(
            description = "音频文件（支持.wav或.mp3格式）",
            type = "string",
            format = "binary"  // 关键：告诉Swagger这是文件
    )
    @JsonProperty("audio_file")
    private MultipartFile audioFileUrl;

    // 音频提示词文本
    @JsonProperty("prompt_text")
    private String promptText;

    // 音色名称 (唯一标识，重复上传会覆盖)
    @JsonProperty("spk_name")
    private String spkName;

    // 音色性别 ("男"/"女")
    @JsonProperty("spk_gender")
    private String spkGender;

    // 音色特点描述（最大20字）
    @JsonProperty("spk_desc")
    @Size(max = 20, message = "音色特点描述最多20字")
    private String spkDesc;

    // 上传用户id
    @JsonProperty("userId")
    private String userId;
}
