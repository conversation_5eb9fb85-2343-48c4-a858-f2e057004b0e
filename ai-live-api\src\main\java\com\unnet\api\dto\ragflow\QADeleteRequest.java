package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "删除QA对请求")
public class QADeleteRequest {

    @Schema(description = "要删除的QA对ID列表", required = true, example = "[\"6000d023cc82a48a\", \"2b6ac08335b6a9d5\"]")
    private List<String> qa_ids;
} 