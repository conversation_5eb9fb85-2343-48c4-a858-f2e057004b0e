package com.unnet.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 聊天响应DTO
 */
@Data
@Schema(description = "聊天响应")
public class ChatResponse {

    @Schema(description = "响应ID")
    private String id;

    @Schema(description = "对象类型")
    private String object;

    @Schema(description = "创建时间")
    private Long created;

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "选择列表")
    private List<Choice> choices;

    @Schema(description = "使用情况")
    private Usage usage;

    /**
     * 选择项
     */
    @Data
    @Schema(description = "选择项")
    public static class Choice {
        
        @Schema(description = "索引")
        private Integer index;

        @Schema(description = "消息")
        private ChatRequest.ChatMessage message;

        @Schema(description = "完成原因")
        private String finishReason;
    }

    /**
     * 使用情况
     */
    @Data
    @Schema(description = "使用情况")
    public static class Usage {
        
        @Schema(description = "提示token数")
        private Integer promptTokens;

        @Schema(description = "完成token数")
        private Integer completionTokens;

        @Schema(description = "总token数")
        private Integer totalTokens;
    }
} 