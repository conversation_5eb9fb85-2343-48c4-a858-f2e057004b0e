package com.unnet.api.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoiceData {

    @JsonProperty("user_id")
    private String userId;

    // 文件路径
    @JsonProperty("file_path")
    private String filePath;

    //试听音频路径
    @JsonProperty("test_path")
    private String testPath;

    // 音频提示词文本
    @JsonProperty("prompt_text")
    private String promptText;

    // 音色名称 (唯一标识，重复上传会覆盖)
    @JsonProperty("spk_name")
    private String spkName;

    // 音色性别 ("男"/"女")
    @JsonProperty("spk_gender")
    private String spkGender;

    // 音色特点描述（最大20字）
    @JsonProperty("spk_desc")
    @Size(max = 20, message = "音色特点描述最多20字")
    private String spkDesc;
}