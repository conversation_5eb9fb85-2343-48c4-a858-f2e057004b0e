package com.unnet.api.aspect;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.api.entity.ApiLog;
import com.unnet.api.service.ApiLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ApiLogAspect {

    private final ApiLogService apiLogService;
    private final ObjectMapper objectMapper;

    @Around("execution(* com.unnet.api.controller.*.*(..))")
    public Object logApiRequest(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Exception exception = null;

        try {
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            try {
                saveApiLog(joinPoint, result, exception, System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.error("记录API日志失败", e);
            }
        }
    }

    private void saveApiLog(ProceedingJoinPoint joinPoint, Object result, Exception exception, long executionTime) throws JsonProcessingException, ExecutionException, InterruptedException {

            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            
            ApiLog apiLog = new ApiLog();
            apiLog.setRequestId(UUID.randomUUID().toString());
            apiLog.setClientId((String) request.getAttribute("clientId"));
            apiLog.setMethod(request.getMethod());
            apiLog.setPath(request.getRequestURI());
            apiLog.setIpAddress(getClientIp(request));
            apiLog.setTimestamp(LocalDateTime.now());
            apiLog.setExecutionTime(executionTime);

            // 记录请求体
            if (joinPoint.getArgs().length > 0) {
                apiLog.setRequestBody(objectMapper.writeValueAsString(joinPoint.getArgs()[0]));
            }

            // 记录响应体
            if (result != null) {
                apiLog.setResponseBody(objectMapper.writeValueAsString(result));
            }

            // 记录错误信息
            if (exception != null) {
                apiLog.setErrorMessage(exception.getMessage());
            }
            
//            CompletableFuture<?> future = CompletableFuture.supplyAsync(() -> {
//            try {
//                return apiLogService.saveApiLog(apiLog).get(); // 阻塞获取结果
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }


//        });
            apiLogService.saveApiLog(apiLog);

    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
} 