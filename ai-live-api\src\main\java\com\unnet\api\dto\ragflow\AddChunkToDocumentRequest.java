package com.unnet.api.dto.ragflow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "向指定文档添加新分段的请求体")
public class AddChunkToDocumentRequest {

    @Schema(description = "分段的文本内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "这是分段的文本内容。")
    private String content;

//    @Schema(description = "与分段相关的关键词或短语列表", example = "[]")
//    private List<String> important_keywords = new ArrayList<>();

//    @Schema(description = "如果提供问题，嵌入的分段将基于这些问题进行优化", example = "[]")
//    private List<String> questions = new ArrayList<>();
} 