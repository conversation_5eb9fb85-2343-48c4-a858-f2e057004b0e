package com.unnet.api.filter;

import com.unnet.api.config.AppProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Component
@Order(2) // 在API Key认证之后执行
@RequiredArgsConstructor
@Slf4j
public class ReplayAttackFilter extends OncePerRequestFilter {

    private static final String NONCE_HEADER = "X-Request-Nonce";
    private static final String TIMESTAMP_HEADER = "X-Request-Timestamp";
    private static final String NONCE_CACHE_NAME = "nonceCache";

    private final AppProperties appProperties;
    private final CacheManager cacheManager;
    private final ObjectMapper objectMapper;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) 
            throws ServletException, IOException {
        
        // 如果防重放功能未启用，则直接放行
        if (!appProperties.getSecurity().getReplayProtection().isEnabled()) {
            filterChain.doFilter(request, response);
            return;
        }
        
        String requestUri = request.getRequestURI();
        log.info("requestUri:{}", requestUri);
        // 如果路径不需要防重放攻击验证，则直接放行
        if (!requiresReplayAttackCheck(requestUri)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 获取请求中的Nonce和时间戳
        String nonce = request.getHeader(NONCE_HEADER);
        String timestampStr = request.getHeader(TIMESTAMP_HEADER);
        
        // 如果请求中没有Nonce或时间戳，则返回400错误
        if (nonce == null || nonce.isEmpty() || timestampStr == null || timestampStr.isEmpty()) {
            handleBadRequest(response, "缺少防重放攻击所需的请求头: " + NONCE_HEADER + " 和 " + TIMESTAMP_HEADER);
            return;
        }
        
        try {
            // 校验时间戳是否在允许的时间窗口内
            long timestamp = Long.parseLong(timestampStr);
            long currentTime = Instant.now().getEpochSecond();
            int tolerance = appProperties.getSecurity().getReplayProtection().getTimestampToleranceSeconds();
            
            if (Math.abs(currentTime - timestamp) > tolerance) {
                handleForbidden(response, "请求时间戳不在允许的时间窗口内");
                return;
            }
            
            // 校验Nonce是否已被使用过
            Cache nonceCache = cacheManager.getCache(NONCE_CACHE_NAME);
            if (nonceCache != null) {
                if (nonceCache.get(nonce) != null) {
                    handleForbidden(response, "请求已被处理过，可能是重放攻击");
                    return;
                }
                
                // 将Nonce存入缓存，标记为已使用
                nonceCache.put(nonce, true);
            } else {
                log.error("Nonce缓存未找到，无法防止重放攻击");
                handleInternalServerError(response, "服务器配置错误");
                return;
            }
            
            // 通过所有校验，继续请求处理链
            filterChain.doFilter(request, response);
            
        } catch (NumberFormatException e) {
            handleBadRequest(response, "无效的时间戳格式");
        }
    }
    
    /**
     * 判断请求路径是否需要防重放攻击检查
     */
    private boolean requiresReplayAttackCheck(String requestUri) {
        // 这里可以配置不需要验证防重放攻击的路径
        String[] whiteListedPaths = {
            "/aiLive/api/v1/tts/api/download_tip_*",    //提示音频、文本下载免校验
            "/actuator/**",
            "/swagger-ui/**",
            "/swagger-ui.html",
            "/swagger-resources/**",
            "/v3/api-docs/**",
            "/v3/api-docs.yaml",
            "/aiLive/swagger-ui/**",
            "/aiLive/swagger-ui.html",
            "/aiLive/v3/api-docs/**",
            "/aiLive/v3/api-docs.yaml",
            "/aiLive/swagger-resources/**",
            "/webjars/**",
            "/aiLive/webjars/**",
            "/error"
        };
        
        for (String path : whiteListedPaths) {
            if (pathMatcher.match(path, requestUri)) {
                return false;
            }
        }
        
        return true;
    }
    
    private void handleBadRequest(HttpServletResponse response, String message) throws IOException {
        sendErrorResponse(response, HttpStatus.BAD_REQUEST, message);
    }
    
    private void handleForbidden(HttpServletResponse response, String message) throws IOException {
        sendErrorResponse(response, HttpStatus.FORBIDDEN, message);
    }
    
    private void handleInternalServerError(HttpServletResponse response, String message) throws IOException {
        sendErrorResponse(response, HttpStatus.INTERNAL_SERVER_ERROR, message);
    }
    
    private void sendErrorResponse(HttpServletResponse response, HttpStatus status, String message) throws IOException {
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        errorDetails.put("status", status.value());
        errorDetails.put("error", status.getReasonPhrase());
        errorDetails.put("message", message);
        
        response.getWriter().write(objectMapper.writeValueAsString(errorDetails));
    }
} 