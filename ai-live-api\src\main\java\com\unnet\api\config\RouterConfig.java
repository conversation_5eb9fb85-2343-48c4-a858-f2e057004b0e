package com.unnet.api.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.api.entity.ServerInfor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.monitor.jvm.HotThreads;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseBody;
import redis.clients.jedis.Jedis;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Future;

@Component
@Slf4j
public class RouterConfig {

    private final List<String> ipList = Arrays.asList(
            "http://***********:50000",
            "http://***********:50010",
            "http://***********:50020",
            "http://***********:50030"
    );
    //节点最大并发数量
    private final List<Integer> maxList = Arrays.asList(
            3,
            3,
            3,
            3
    );

    @Value("${redis.host}")
    private String redisHost;

    @Value("${redis.port}")
    private int redisPort;

    @Value("${redis.database}")
    private int redisDatabase;

    @Value("${redis.password}")
    private String redisPassword;

    @Resource
    private ObjectMapper objectMapper;

    //查询缓存ip节点信息
    public String getIpInfo() {
        String result = null;
        //查询各个服务器状况
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            return jedis.get("servers");
        }
    }

    //设置缓存ip节点信息
    public void setIpInfo(String servers) {
        //查询各个服务器状况
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            jedis.set("servers", servers);
        }
    }

    //设置流式输出缓存数据  并设置缓存时间
    public void setRedisInfoBySecond(String key, String info, long seconds) {
        //查询各个服务器状况
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            jedis.setex(key,seconds,info);
        }
    }

    public void setRedisInfo(String key, String info) {
        //查询各个服务器状况
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            jedis.set(key,info);
        }
    }

    //获取缓存数据
    public String getRedisInfo(String key) {
        //查询各个服务器状况
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            return jedis.get(key);
        }
    }

    //查询缓存获取合适服务器ip
    public String findIp() throws JsonProcessingException {
        String result = null;
        //查询各个服务器状况
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }

            String serverListStr = jedis.get("servers");
            //如果还没有服务器信息，则初始化
            if(serverListStr == null || serverListStr.isEmpty()){
                initialize();
                result= ipList.get(0);
            }
            else{
                List<ServerInfor> serverList = objectMapper.readValue(serverListStr, new TypeReference<List<ServerInfor>>() {});
                //写死测试，返回第一个
                log.info("serverList:{}", serverList.toString());
//                return serverList.get(0).getIp();

                int max = 0;
                int index = 0;
                for(int i = 0 ; i<  serverList.size(); i++)
                {
                    int num = serverList.get(i).getMaxNum()- serverList.get(i).getNum();
                    if( num > max ) {
                        max = num ;
                        index = i;
                    }
                }
                if(max == 0)throw new Exception("no server available");
                serverList.get(index).setNum(serverList.get(index).getNum()+1);
                String json = objectMapper.writeValueAsString(serverList);
                jedis.set("servers",json);

                result = ipList.get(index);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    //初始化缓存数据
    @PostConstruct
    public void initialize() throws JsonProcessingException {
        List<ServerInfor> serverList =  new ArrayList<>();
        for(int i = 0; i < ipList.size(); i++){
            ServerInfor serverInfor = new ServerInfor(ipList.get(i), 0, maxList.get(i));
            serverList.add(serverInfor);
        }
        try  (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            jedis.select(redisDatabase);
            String json = objectMapper.writeValueAsString(serverList);
            jedis.set("servers",json);
        }
    }

    //在redis中存储 eventId,Url键值对
    public  void SetIpEvent(String eventId, String apiUrl) {

        Map<String,String> map = new HashMap<>();
        map.put(eventId,apiUrl);
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            jedis.select(redisDatabase);
            jedis.hmset(eventId,map);
            jedis.expire(eventId, 6 * 60 * 60);// 设置过期时间为6个小时（以秒为单位）
        }
    }

    //在redis中获取 eventId,Url键值对
    public String getIpEvent(String eventId) throws Exception {

        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }

            jedis.select(redisDatabase);
            Map<String,String> map = jedis.hgetAll(eventId);
            if (map != null && !map.isEmpty()) {
                // 假设每个 eventId 对应一个 apiUrl，这里直接返回第一个值
                return map.values().iterator().next();
            }else throw new Exception("eventId not found");
        }
    }

    //获取在redis中缓存信息
    public  String getInfo(String spkName, String userid, String type) throws Exception {
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }

            jedis.select(redisDatabase);
            return jedis.get(spkName+userid+type);
        } catch (Exception e) {
            throw new Exception("eventId not found");
        }
    }

    //在redis中缓存信息
    public  void storeInfo(String spkName, String userid, String filePath, String type) {
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            // 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }

            jedis.select(redisDatabase);
            jedis.set(spkName+userid+type,filePath);
        }
    }


    //异步查询流式接口任务状态
    @Async("streamExecutor")
    public void getStatus(String eventId) throws Exception {
        log.info("------进入异步查询流式接口任务状态接口");
        String API_URL = getIpEvent(eventId);
        String sourceUrl = API_URL + "/gradio_api/call/generate_audio/" + eventId;
        HttpURLConnection connection = (HttpURLConnection) new URL(sourceUrl).openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Accept", "text/event-stream");

        // 3. 流式转发
        try (BufferedReader source = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            String line;
            int i = 1;
            Boolean complete = false;
            //连续心跳数量
            int heartbeatSum = 0;
            List<String> redisList = new ArrayList<>();
            StringBuilder dataSegment = new StringBuilder();
            while ((line = source.readLine()) != null) {
                log.info("line{}:{}", i++, line);
                if(line.startsWith("event")) {
                    if(line.startsWith("event: heartbeat")) {
                        heartbeatSum++;
                    } else { //其他非心跳的事件，则重置计数器
                        heartbeatSum=0;
                    }
                    //如果连续10个心跳 都没有数据生成，则终止流式数据推送
                    if(heartbeatSum >= 30) {
                        break;
                    }
                }
                // 处理data开头的行
                if (line.startsWith("data:")) {
                    try {
                        // 提取JSON部分
                        String jsonStr = line.substring(line.indexOf(':') + 1).trim();
                        if(!jsonStr.endsWith("null")) {  //跳过null字符串 不处理
                            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
                            // 修改每个对象中的url
                            for (int j = 0; j < jsonArray.size(); j++) {
                                JSONObject obj = jsonArray.getJSONObject(j);
                                if (obj != null && !"".equals(obj.getString("url")) && obj.getString("url") != null) {
                                    String originalUrl = obj.getString("url");
                                    // 替换IP端口和路径
                                    String modifiedUrl = originalUrl
                                            .replaceFirst("//[^/]+/", "//***********:8081/")
                                            .replace("/gradio_a/gradio_api/", "/aiLive/api/v1/tts/");
                                    obj.put("url", modifiedUrl);
                                }
                            }
                            // 重新构建data行
                            line = "data:" + jsonArray.toString();
                        }
                    } catch (Exception e) {
                        log.info("JSON处理失败: {}", line);
                        // 如果处理失败，保持原样输出
                    }
                }

                // 实时获取原始数据并写入缓存，key是event_id
                if(line.startsWith("event:")) {
                    dataSegment.append(line);
                } else if(line.startsWith("data:")) {
                    dataSegment.append("\n").append(line);
                    redisList.add(dataSegment.toString());
                    dataSegment.setLength(0); //清空sb
                    setRedisInfoBySecond(eventId+"_res", JSONObject.toJSONString(redisList), 600);
                }

                if(complete) {
                    break;
                }
                // 如果检测到complete/error事件可在下一行推送完成后终止
                if (line.startsWith("event: complete") || line.startsWith("event: error")) {
                    complete = true;
                }
            }
        } catch (Exception e) {
            log.info("流式获取音频结果信息异常, event_id：{}", eventId);
        } finally {
            //减少负载数量
            numMinus(API_URL);
        }
    }

    public void numMinus(String API_URL) throws JsonProcessingException {
        try(Jedis jedis = new Jedis(redisHost, redisPort)){// 如果有密码，则进行认证
            if (!redisPassword.isEmpty()) {
                jedis.auth(redisPassword);
            }
            jedis.select(redisDatabase);
            String serverListStr = jedis.get("servers");
            List<ServerInfor> serverList = objectMapper.readValue(serverListStr, new TypeReference<List<ServerInfor>>() {});
            for(int i = 0 ;i<serverList.size() ;i++){
                if(serverList.get(i).getIp().equals(API_URL)){
                    log.info("减少占用前-当前节点:{} 占用算力：{} 最大算力:{}",serverList.get(i).getIp(), serverList.get(i).getNum(),serverList.get(i).getMaxNum());
                    if(serverList.get(i).getNum()-1 > 0) {
                        serverList.get(i).setNum(serverList.get(i).getNum() - 1);
                    } else {
                        serverList.get(i).setNum(0);
                    }
                    break;
                }
            }
            String json = objectMapper.writeValueAsString(serverList);
            jedis.set("servers",json);
        }
    }
}
